import logging
import os
from datetime import datetime
from pathlib import Path
from app.config.config import settings

EXECUTE_LEVEL = 25
logging.addLevelName(EXECUTE_LEVEL, "EXECUTE")


def execute(self, message, *args, **kwargs):
    if self.isEnabledFor(EXECUTE_LEVEL):
        self._log(EXECUTE_LEVEL, message, args, kwargs)


logging.Logger.execute = execute


# --- Custom Filter Class ---
class EnhancedLogFilter(logging.Filter):

    def __init__(self, allowed_loggers=None, excluded_loggers=None):
        """
        Initialize the log filter.

        :param allowed_loggers: Set of loggers to allow. If None, allows all.
        :param excluded_loggers: Set of loggers to exclude.
        """
        super().__init__()
        self.allowed_loggers = allowed_loggers or set()
        self.excluded_loggers = excluded_loggers or set()

    def filter(self, record):
        """Filter logs based on allowed and excluded loggers."""
        if record.name in self.excluded_loggers:
            return False

        if self.allowed_loggers and record.name not in self.allowed_loggers:
            return False

        return True


def get_log_level():
    """
    Fetch log level dynamically from environment variables.
    If `APP_DEBUG` is set to `"True"`, use DEBUG, else INFO.
    """
    app_debug = settings.app_debug
    return logging.DEBUG if app_debug else logging.INFO


def setup_logging(
    allowed_loggers=None,
    excluded_loggers=None,
    log_format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    date_format="%Y-%m-%d %H:%M:%S",
    enable_file_logging=True,
    log_file_name=None,
):
    """
    Set up logging for the entire application dynamically.
    - Ensures only the root logger has handlers.
    - Prevents duplicate logs from child loggers.
    - Optionally writes logs to files in logs/ directory.
    """
    level = get_log_level()

    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    if root_logger.hasHandlers():
        root_logger.handlers.clear()

    # Console handler
    console_handler = logging.StreamHandler()
    formatter = logging.Formatter(log_format, datefmt=date_format)
    console_handler.setFormatter(formatter)
    console_handler.addFilter(EnhancedLogFilter(allowed_loggers, excluded_loggers))
    root_logger.addHandler(console_handler)

    # File handler (if enabled)
    if enable_file_logging:
        # Create logs directory if it doesn't exist
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)

        # Generate log file name if not provided
        if log_file_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file_name = f"orchestration_engine_{timestamp}.log"

        log_file_path = logs_dir / log_file_name

        file_handler = logging.FileHandler(log_file_path, mode='a', encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.addFilter(EnhancedLogFilter(allowed_loggers, excluded_loggers))
        root_logger.addHandler(file_handler)

        # Log the file location
        root_logger.info(f"Logging to file: {log_file_path.absolute()}")


def get_logger(name):
    """
    Get a logger with the given name.
    Ensures child loggers inherit from the root logger without extra handlers.
    """
    logger = logging.getLogger(name)
    logger.propagate = True
    return logger
