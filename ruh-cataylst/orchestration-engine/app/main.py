import argparse
import asyncio
import traceback
from app.utils.enhanced_logger import setup_logging, get_logger
from app.execution.executor_server_kafka import consume
from app.execution.run_engine import run_engine
from app.config.config import settings

setup_logging(
    allowed_loggers={
        "EnhancedWorkflowEngine",
        "TransitionHandler",
        "WorkflowUtils",
        "StateManager",
        "RedisManager",
        "PostgresManager",
        "RedisEventListener",
        "Main",
        "AgentExecutor",
        "KafkaToolExecutor",
        "KafkaWorkflowConsumer",
        "MCPToolExecutor",
        "NodeExecutor",
        "WorkflowService",
        "HelperFunctions",
        "WorkflowStateManager",
    },
    excluded_loggers={"httpx", "mcp.client.sse"},
    enable_file_logging=True,
)

logger = get_logger("Main")


def start_server():
    try:
        logger.info(f"Connection at: {settings.kafka_bootstrap_servers}")
        asyncio.run(consume())
    except KeyboardInterrupt:
        logger.error("Shutting down due to keyboard interrupt...")
    except Exception as e:
        logger.error(f"Error occurred: {e}:" + traceback.format_exc())


def start_engine():
    try:
        logger.debug("Engine Started")
        asyncio.run(run_engine())
    except KeyboardInterrupt:
        logger.error("Shutting down due to keyboard interrupt...")
    except Exception as e:
        logger.error(f"Error occurred: {e}")
    finally:
        logger.debug("Async execution completed")


def main():
    logger.info("Starting Server")
    parser = argparse.ArgumentParser(
        description="Choose which part of the script to run."
    )
    parser.add_argument(
        "--mode",
        type=str,
        required=True,
        choices=["server", "engine"],
        help="Mode to run the script in.",
    )

    args = parser.parse_args()

    if args.mode == "server":
        start_server()
    elif args.mode == "engine":
        start_engine()


if __name__ == "__main__":
    """
    Usage:
        To run the  kafka server:
        poetry run python -m app.main --mode server

        To run the engine:
        poetry run python -m app.main --mode engine
    """
    main()
