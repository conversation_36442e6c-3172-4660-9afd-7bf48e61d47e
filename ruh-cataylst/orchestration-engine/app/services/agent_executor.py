# agent_executor.py

import asyncio
import json
import uuid
from typing import Any, Dict, Optional

from aiokafka import AIOKafkaProducer, AIOKafkaConsumer  # type: ignore
from aiokafka.errors import KafkaError  # type: ignore
from app.config.config import settings
from app.utils.enhanced_logger import get_logger


logger = get_logger("AgentExecutor")


class AgentExecutionError(Exception):
    pass


class AgentExecutor:
    def __init__(self, producer: AIOKafkaProducer):
        self.logger = logger
        if producer is None:
            raise ValueError("A running AIOKafkaProducer instance must be provided.")
        if not getattr(producer._sender, "_running", True):
            self.logger.warning("The provided Kafka Producer may not be running.")
        self.producer = producer
        self._bootstrap_servers = settings.kafka_bootstrap_servers
        self._request_topic = settings.kafka_agent_execution_request_topic
        self._results_topic = settings.kafka_agent_execution_result_topic

        self._consumer: Optional[AIOKafkaConsumer] = None
        self._consumer_task: Optional[asyncio.Task] = None
        self._pending_requests: Dict[str, asyncio.Future] = {}
        self._consumer_group_id = f"agent-executor-consumer"
        self._current_correlation_id: Optional[str] = None
        self._current_user_id: Optional[str] = None
        self.logger.info("AgentExecutor initialized.")

    async def _start_internal_consumer(self):
        if self._consumer is not None:
            self.logger.warning("Internal consumer already started.")
            return

        self.logger.info("Starting AgentExecutor internal consumer...")
        try:
            self._consumer = AIOKafkaConsumer(
                self._results_topic,
                bootstrap_servers=self._bootstrap_servers,
                group_id=self._consumer_group_id,
                auto_offset_reset="latest",
                enable_auto_commit=True,
            )
            await self._consumer.start()
            self.logger.info(
                f"Internal consumer started. Listening for results on: '{self._results_topic}', Group: '{self._consumer_group_id}'"
            )

            self._consumer_task = asyncio.create_task(
                self._consume_loop(),
                name=f"AgentExecutorConsumer-{self._consumer_group_id[:8]}",
            )
            self.logger.info("Background result consumer loop started.")

        except KafkaError as e:
            self.logger.error(f"Failed to start internal consumer: {e}", exc_info=True)
            await self._stop_internal_consumer()
            raise

    async def _stop_internal_consumer(self):
        self.logger.info("Stopping AgentExecutor internal consumer components...")

        if self._consumer_task and not self._consumer_task.done():
            self.logger.debug("Cancelling background consumer task...")
            self._consumer_task.cancel()
            try:
                await self._consumer_task
            except asyncio.CancelledError:
                self.logger.debug("Consumer task successfully cancelled.")
            except Exception as e:
                self.logger.error(
                    f"Error during consumer task cancellation: {e}", exc_info=True
                )
        self._consumer_task = None

        if self._consumer:
            self.logger.debug("Stopping internal Kafka consumer...")
            try:
                await self._consumer.stop()
                self.logger.info("Internal Kafka consumer stopped.")
            except Exception as e:
                self.logger.error(f"Error stopping consumer: {e}", exc_info=True)
            self._consumer = None

        if self._pending_requests:
            self.logger.warning(
                f"Stopping internal consumer with {len(self._pending_requests)} pending requests."
            )
            for request_id, future in self._pending_requests.items():
                if not future.done():
                    future.set_exception(
                        AgentExecutionError(
                            f"Executor stopped before result received. Cancelled request_id:{request_id}"
                        )
                    )
            self._pending_requests.clear()

        self.logger.info("AgentExecutor internal consumer stopped.")

    async def start(self):
        await self._start_internal_consumer()

    async def stop(self):
        await self._stop_internal_consumer()

    async def __aenter__(self):
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.stop()

    def set_correlation_id(self, correlation_id: str):
        """
        Set the current correlation ID for this executor instance.
        This ID will be included in all agent execution requests.

        Args:
            correlation_id: The correlation ID to use for subsequent requests
        """
        self._current_correlation_id = correlation_id
        self.logger.debug(f"Set correlation ID to: {correlation_id}")

    def set_user_id(self, user_id: str):
        """
        Set the current user ID for this executor instance.
        This ID will be included in all agent execution requests.

        Args:
            user_id: The user ID to use for subsequent requests
        """
        self._current_user_id = user_id
        self.logger.debug(f"Set user ID to: {user_id}")

    async def _consume_loop(self):
        if not self._consumer:
            self.logger.error("Consumer not initialized in _consume_loop.")
            return

        try:
            while True:
                try:
                    async for msg in self._consumer:
                        self.logger.debug(
                            f"Result consumer received message: Offset={msg.offset}"
                        )
                        try:
                            result_payload = json.loads(msg.value.decode("utf-8"))
                            request_id = result_payload.get("request_id")

                            if result_payload.get("success"):
                                result_data = result_payload.get("agent_response").get("content")
                            else:
                                error_data="Encountered error during Agent exception"

                            if not request_id:
                                self.logger.warning(
                                    f"Received result message without 'request_id': {result_payload}"
                                )
                                continue

                            future = self._pending_requests.pop(request_id, None)

                            if future and not future.done():
                                if error_data:
                                    self.logger.warning(
                                        f"Received error response for request_id {request_id}: {error_data}"
                                    )
                                    future.set_exception(
                                        AgentExecutionError(
                                            f"Agent execution failed: {error_data}"
                                        )
                                    )
                                else:
                                    self.logger.debug(
                                        f"Received valid result for request_id {request_id}"
                                    )
                                    future.set_result(result_data)
                            elif future and future.done():
                                self.logger.warning(
                                    f"Received result for already completed/cancelled request_id {request_id}"
                                )
                            else:
                                self.logger.warning(
                                    f"Received result for unknown or timed-out request_id: {request_id}"
                                )

                        except json.JSONDecodeError:
                            self.logger.warning(
                                f"Could not decode JSON from results topic: {msg.value.decode('utf-8', errors='ignore')}"
                            )
                        except Exception as e:
                            self.logger.error(
                                f"Error processing result message: {e}", exc_info=True
                            )

                except asyncio.CancelledError:
                    self.logger.info("Result consumer loop cancelled.")
                    break
        except Exception as e:
            self.logger.error(
                f"Result consumer loop unexpectedly terminated: {e}", exc_info=True
            )
            if self._pending_requests:
                err = AgentExecutionError(f"Consumer loop failed: {e}")
                for req_id, fut in self._pending_requests.items():
                    if not fut.done():
                        fut.set_exception(err)

    async def execute_tool(
        self,
        tool_name: str = None,
        tool_parameters: dict = None,
        transition_id: str = None,
    ) -> Any:
        """
        Execute an agent task based on tool_parameters.

        The tool_parameters should contain:
        - agent_type: "component", "employee", "A2A", or "ACP"
        - execution_type: "response" or "interactive"
        - query: The main user query to be relayed
        - agent_config: Complete agent configuration object

        Args:
            tool_name: The name of the agent tool/task
            tool_parameters: Parameters containing agent_type, execution_type, query, and agent_config
            transition_id: The transition ID from the orchestration engine

        Returns:
            The result from the agent execution
        """
        if not self._consumer or not self._consumer_task or self._consumer_task.done():
            raise RuntimeError(
                "AgentExecutor's internal consumer is not running. Call start() or use 'async with'."
            )

        if not self.producer or not getattr(self.producer._sender, "_running", True):
            raise RuntimeError(
                "The provided Kafka Producer is not running or not available."
            )

        request_id = str(uuid.uuid4())

        # Extract parameters from the new structure
        agent_type = tool_parameters.get("agent_type", "component")
        execution_type = tool_parameters.get("execution_type", "response")
        query = tool_parameters.get("query")
        agent_config = tool_parameters.get("agent_config")
        tools = agent_config.get("agent_tools", [])
        normalized_tools = [{"mcp_id": tool.get("mcp_id", ""), "tool_name": tool.get("tool_name", "")} for tool in tools]
        
        normalized_agent_config = {
            "id": str(uuid.uuid4()),
            "name": agent_config.get("name", "AI Agent"),
            "description": agent_config.get("description", "Basic AI Agent"),
            "system_message": agent_config.get("system_message"),
            "model_config": agent_config.get("model_config"),
            "mcps": normalized_tools,
        }

        if execution_type == "interactive":
            normalized_agent_config["termination_condition"] = agent_config.get("termination_condition", "TERMINATE")
        
        # Validate agent_type
        valid_agent_types = ["component", "employee", "A2A", "ACP"]
        if agent_type not in valid_agent_types:
            raise ValueError(
                f"Invalid agent_type '{agent_type}'. Must be one of: {valid_agent_types}"
            )

        # Validate execution_type
        valid_execution_types = ["response", "interactive"]
        if execution_type not in valid_execution_types:
            raise ValueError(
                f"Invalid execution_type '{execution_type}'. Must be one of: {valid_execution_types}"
            )

        # Build context info for logging
        context_info = []
        if self._current_correlation_id:
            context_info.append(f"correlation_id: {self._current_correlation_id}")
        if self._current_user_id:
            context_info.append(f"user_id: {self._current_user_id}")

        context_str = f" with {', '.join(context_info)}" if context_info else ""

        self.logger.info(
            f"Executing agent '{tool_name}' type '{agent_type}' execution '{execution_type}' via Kafka (request_id: {request_id}){context_str} using provided producer."
        )

        # Process based on agent type
        if agent_type == "component":
            message_request = await self._build_component_agent_request(
                normalized_agent_config, query, execution_type, request_id, tool_parameters, transition_id
            )
        elif agent_type == "employee":
            message_request = await self._build_employee_agent_request(
                normalized_agent_config, query, execution_type, request_id, tool_parameters, transition_id
            )
        elif agent_type == "A2A":
            message_request = await self._build_a2a_agent_request(
                normalized_agent_config, query, execution_type, request_id, tool_parameters, transition_id
            )
        elif agent_type == "ACP":
            message_request = await self._build_acp_agent_request(
                normalized_agent_config, query, execution_type, request_id, tool_parameters, transition_id
            )
        else:
            raise ValueError(f"Unsupported agent_type: {agent_type}")

        # Add correlation_id to the payload if it's set
        if self._current_correlation_id:
            message_request["correlation_id"] = self._current_correlation_id
            self.logger.debug(
                f"Added correlation_id {self._current_correlation_id} to payload"
            )

        future = asyncio.Future()
        self._pending_requests[request_id] = future

        try:
            self.logger.debug(
                f"Sending request to topic '{self._request_topic}': {message_request}"
            )
            await self.producer.send(self._request_topic, value=message_request)
            self.logger.debug(
                f"Request {request_id} sent successfully using provided producer."
            )

            # Handle different execution types differently
            if execution_type == "interactive":
                self.logger.info(
                    f"Interactive session started for request {request_id}. "
                    f"Waiting for final session result only..."
                )
                # For interactive sessions, we only wait for the final result
                # Intermediate chat messages go directly to user via workflow-responses topic
                result = await future
                self.logger.info(
                    f"Interactive session completed for request {request_id}. "
                    f"Final result received for next workflow node."
                )
            else:
                self.logger.debug(
                    f"Waiting for single response result for request {request_id}..."
                )
                result = await future
                self.logger.info(f"Single response received for request {request_id}.")

            return result

        except KafkaError as e:
            self.logger.error(
                f"Kafka error during agent execution {request_id}: {e}", exc_info=True
            )
            self._pending_requests.pop(request_id, None)
            raise AgentExecutionError(
                f"Kafka error executing request {request_id}: {e}"
            ) from e
        except Exception as e:
            self.logger.error(
                f"Error during agent execution {request_id}: {e}", exc_info=True
            )
            self._pending_requests.pop(request_id, None)
            raise e

    async def _build_component_agent_request(
        self,
        agent_config: dict,
        query: str,
        execution_type: str,
        request_id: str,
        tool_parameters: dict,
        transition_id: str = None,
    ) -> dict:
        """Build request for component agent type."""
        self.logger.info(
            f"Building component agent request for execution_type: {execution_type}"
        )

        # Convert to new format
        current_format_request = {
            "request_id": request_id,
            "user_id": self._current_user_id,
            "correlation_id": self._current_correlation_id,
            "agent_type": tool_parameters.get("agent_type", "component"),
            "execution_type": execution_type,
            "query": query,
            "variables": tool_parameters.get("variables", {}),
            "agent_config": agent_config,
        }

        return current_format_request

    async def _build_employee_agent_request(
        self,
        agent_config: dict,
        query: str,
        execution_type: str,
        request_id: str,
        tool_parameters: dict,
        transition_id: str = None,
    ) -> dict:
        """Build request for employee agent type - PLACEHOLDER."""
        self.logger.warning(
            "Employee agent type not yet implemented - using component agent as fallback"
        )

        # TODO: Implement employee-specific logic
        # For now, use component agent structure as base
        base_request = await self._build_component_agent_request(
            agent_config, query, execution_type, request_id, tool_parameters, transition_id
        )
        base_request["agent_type"] = "employee"

        # TODO: Add employee-specific fields:
        # - employee_id
        # - department
        # - role_permissions
        # - escalation_rules

        return base_request

    async def _build_a2a_agent_request(
        self,
        agent_config: dict,
        query: str,
        execution_type: str,
        request_id: str,
        tool_parameters: dict,
        transition_id: str = None,
    ) -> dict:
        """Build request for A2A (Agent-to-Agent) agent type - PLACEHOLDER."""
        self.logger.warning(
            "A2A agent type not yet implemented - using component agent as fallback"
        )

        # TODO: Implement A2A-specific logic
        # For now, use component agent structure as base
        base_request = await self._build_component_agent_request(
            agent_config, query, execution_type, request_id, tool_parameters, transition_id
        )
        base_request["agent_type"] = "A2A"

        # TODO: Add A2A-specific fields:
        # - source_agent_id
        # - target_agent_id
        # - communication_protocol
        # - handoff_context

        return base_request

    async def _build_acp_agent_request(
        self,
        agent_config: dict,
        query: str,
        execution_type: str,
        request_id: str,
        tool_parameters: dict,
        transition_id: str = None,
    ) -> dict:
        """Build request for ACP agent type - PLACEHOLDER."""
        self.logger.warning(
            "ACP agent type not yet implemented - using component agent as fallback"
        )

        # TODO: Implement ACP-specific logic
        # For now, use component agent structure as base
        base_request = await self._build_component_agent_request(
            agent_config, query, execution_type, request_id, tool_parameters, transition_id
        )
        base_request["agent_type"] = "ACP"

        # TODO: Add ACP-specific fields:
        # - acp_context
        # - process_definition
        # - workflow_state
        # - decision_points

        return base_request

    def _get_default_agent_config(self) -> dict:
        """Get a default agent configuration for component agents."""
        return {
            "id": "orchestration-agent-001",
            "name": "Orchestration Assistant",
            "description": "A helpful assistant for orchestration workflows",
            "system_message": (
                "You are a helpful assistant integrated into an orchestration workflow. "
                "Respond to user queries in a friendly and informative manner."
            ),
            "model_config": {
                "model_provider": "openai",
                "model": "gpt-4o-mini",
                "temperature": 0.7,
                "max_tokens": 1000,
            },
            "termination_condition": "auto",
            "tools": [],
            "capabilities": ["general_assistance", "workflow_support"],
        }

   
