#!/usr/bin/env python3
"""
Test script to reproduce terminal loop behavior issue.

This script creates a simple workflow with a loop node that has NO exit transitions
to verify if the workflow terminates correctly after sending final results.
"""

import asyncio
import json
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_terminal_loop_workflow() -> Dict[str, Any]:
    """
    Create a workflow with a terminal loop (no exit transitions).
    
    The workflow structure:
    1. Input node -> provides initial data
    2. Loop node -> processes data with NO exit transitions (terminal)
    
    Expected behavior:
    - Loop executes iterations
    - Loop sends final results via result callback
    - Workflow should terminate (but currently doesn't according to user)
    """
    
    workflow_schema = {
        "workflow_id": "terminal_loop_test",
        "workflow_name": "Terminal Loop Test Workflow",
        "nodes": [
            {
                "id": "input_node",
                "name": "Input Node",
                "type": "component",
                "tool_name": "CombineTextComponent",
                "sequence": 1
            },
            {
                "id": "terminal_loop_node", 
                "name": "Terminal Loop Node",
                "type": "loop",
                "tool_name": "LoopNode",
                "sequence": 2
            }
        ],
        "transitions": [
            {
                "id": "transition-input-node",
                "name": "Input Transition",
                "sequence": 1,
                "type": "standard",
                "execution_type": "component",
                "node_info": {
                    "node_id": "input_node",
                    "tool_name": "CombineTextComponent",
                    "output_data": [
                        {
                            "to_transition_id": "transition-terminal-loop-node",
                            "output_handle_registry": {
                                "handle_mappings": [
                                    {
                                        "result_path": "combined_text",
                                        "target_parameter": "start",
                                        "target_transition_id": "transition-terminal-loop-node"
                                    }
                                ]
                            }
                        }
                    ]
                },
                "tool_params": {
                    "text_1": "1",
                    "text_2": "",
                    "separator": ""
                }
            },
            {
                "id": "transition-terminal-loop-node",
                "name": "Terminal Loop Transition", 
                "sequence": 2,
                "type": "standard",
                "execution_type": "loop",
                "node_info": {
                    "node_id": "terminal_loop_node",
                    "tool_name": "LoopNode",
                    # CRITICAL: NO output_data configured - this makes it terminal
                    "output_data": []
                },
                "loop_config": {
                    "source_type": "number_range",
                    "start": None,  # Will be provided by handle mapping from input
                    "end": 5,
                    "step": 1,
                    "batch_size": 1,
                    "parallel_execution": True,
                    "max_concurrent": 2,
                    "preserve_order": True,
                    "iteration_timeout": 30,
                    "aggregation_type": "collect_all",
                    "include_metadata": True,
                    "on_iteration_error": "continue",
                    "include_errors": True,
                    # CRITICAL: NO exit_transition configured
                    # CRITICAL: NO loop_body_transitions configured - makes it truly terminal
                    "loop_body_transitions": []
                }
            }
        ]
    }
    
    return workflow_schema

def create_workflow_execution_payload() -> Dict[str, Any]:
    """Create the payload for workflow execution."""
    
    payload = {
        "user_dependent_fields": ["text_1"],
        "user_payload_template": {
            "text_1": {
                "value": "1",
                "transition_id": "transition-input-node"
            }
        }
    }
    
    return payload

async def test_terminal_loop_behavior():
    """
    Test the terminal loop behavior by creating and analyzing the workflow.
    
    This function doesn't actually execute the workflow but creates the test case
    that can be used to reproduce the issue.
    """
    
    logger.info("🧪 Creating terminal loop test case...")
    
    # Create the workflow schema
    workflow_schema = create_terminal_loop_workflow()
    
    # Create the execution payload
    execution_payload = create_workflow_execution_payload()
    
    # Save the test case files
    test_case = {
        "description": "Terminal Loop Test Case",
        "issue": "Loop with no exit transitions sends results but doesn't terminate workflow",
        "expected_behavior": "Workflow should terminate after loop sends final results",
        "actual_behavior": "Workflow continues running and doesn't terminate",
        "workflow_schema": workflow_schema,
        "execution_payload": execution_payload,
        "test_instructions": [
            "1. Use this workflow schema to create a workflow",
            "2. Execute the workflow with the provided payload",
            "3. Observe that the loop executes and sends results",
            "4. Verify that the workflow does NOT terminate (current bug)",
            "5. Expected fix: workflow should terminate after loop completion"
        ]
    }
    
    # Save to file for easy testing
    with open("terminal_loop_test_case.json", "w") as f:
        json.dump(test_case, f, indent=2)
    
    logger.info("✅ Terminal loop test case created successfully!")
    logger.info("📁 Saved to: terminal_loop_test_case.json")
    
    # Print key insights
    logger.info("\n🔍 Key Configuration Points:")
    logger.info("1. Loop node has empty output_data: []")
    logger.info("2. Loop config has no exit_transition")
    logger.info("3. Loop config has empty loop_body_transitions: []")
    logger.info("4. This should make the loop terminal")
    
    logger.info("\n🐛 Expected Issue:")
    logger.info("- Loop will execute iterations (1 to 5)")
    logger.info("- Loop will send final aggregated results")
    logger.info("- _handle_loop_completion should return empty list")
    logger.info("- Executor core should set terminated=True")
    logger.info("- But workflow doesn't actually terminate")
    
    return test_case

if __name__ == "__main__":
    asyncio.run(test_terminal_loop_behavior())
