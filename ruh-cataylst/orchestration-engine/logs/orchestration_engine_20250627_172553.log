2025-06-27 17:25:53 - Main - INFO - Starting Server
2025-06-27 17:25:53 - Main - INFO - Connection at: **************:9092
2025-06-27 17:25:53 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-27 17:25:53 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-27 17:25:53 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-27 17:25:53 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-27 17:25:53 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-27 17:25:55 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-27 17:25:55 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-27 17:25:56 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
2025-06-27 17:25:58 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-27 17:25:58 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-27 17:26:01 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-27 17:26:01 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-27 17:26:01 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-27 17:26:03 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-27 17:26:03 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-27 17:26:05 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-27 17:26:05 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-27 17:26:05 - RedisEventListener - INFO - Redis event listener started
2025-06-27 17:26:05 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-27 17:26:05 - StateManager - DEBUG - Using provided database connections
2025-06-27 17:26:05 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-27 17:26:05 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-27 17:26:05 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-27 17:26:05 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-27 17:26:05 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-27 17:26:05 - StateManager - INFO - WorkflowStateManager initialized
2025-06-27 17:26:05 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-27 17:26:05 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-27 17:26:06 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-27 17:26:06 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-27 17:26:08 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-27 17:26:08 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-27 17:26:08 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-27 17:26:13 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-27 17:26:19 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-27 17:26:19 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-27 17:26:19 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-27 17:26:25 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-27 17:26:25 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-27 17:26:25 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-27 17:26:32 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-27 17:26:32 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-27 17:26:32 - KafkaWorkflowConsumer - INFO - Received: topic=approval-requests, partition=0, offset=218
2025-06-27 17:26:32 - KafkaWorkflowConsumer - ERROR - No running workflow found for correlationId: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5 (approval-request)
2025-06-27 17:26:32 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5, response: {'status': 'error', 'workflow_status': 'failed', 'result': 'No workflow found for approval request', 'message': 'No workflow found for approval request', 'sequence': 0}
2025-06-27 17:26:32 - KafkaWorkflowConsumer - INFO - Received: topic=approval-requests, partition=0, offset=219
2025-06-27 17:26:32 - KafkaWorkflowConsumer - ERROR - No running workflow found for correlationId: 808deac0-5943-4173-b272-749330a67ffe (approval-request)
2025-06-27 17:26:32 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 808deac0-5943-4173-b272-749330a67ffe, response: {'status': 'error', 'workflow_status': 'failed', 'result': 'No workflow found for approval request', 'message': 'No workflow found for approval request', 'sequence': 0}
2025-06-27 17:26:32 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=966
2025-06-27 17:26:32 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751023620, 'task_type': 'workflow', 'data': {'workflow_id': '50604f34-6f45-4b9d-a32c-d16691a2d80a', 'payload': {'user_dependent_fields': ['main_input'], 'user_payload_template': {'main_input': {'value': '1', 'transition_id': 'CombineTextComponent-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-27 17:26:32 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 50604f34-6f45-4b9d-a32c-d16691a2d80a
2025-06-27 17:26:32 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/50604f34-6f45-4b9d-a32c-d16691a2d80a
2025-06-27 17:26:33 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-27 17:26:33 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Untitled Workflow retrieved successfully",
  "workflow": {
    "id": "50604f34-6f45-4b9d-a32c-d16691a2d80a",
    "name": "Untitled Workflow",
    "description": "Untitled_Workflow",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/8a519087-837e-471a-96ad-f9372e5a95e3.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/760f412e-5db0-42f4-a077-4c570c061e6a.json",
    "start_nodes": [
      {
        "field": "main_input",
        "type": "string",
        "transition_id": "transition-CombineTextComponent-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-27T08:01:16.036804",
    "updated_at": "2025-06-27T11:56:21.455322",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      }
    ],
    "is_updated": true
  }
}
2025-06-27 17:26:34 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 50604f34-6f45-4b9d-a32c-d16691a2d80a - server_script_path is optional
2025-06-27 17:26:34 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-27 17:26:34 - StateManager - DEBUG - Using global database connections from initializer
2025-06-27 17:26:34 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-27 17:26:34 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-27 17:26:34 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-27 17:26:34 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-27 17:26:34 - StateManager - INFO - WorkflowStateManager initialized
2025-06-27 17:26:34 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-27 17:26:34 - StateManager - DEBUG - Using provided database connections
2025-06-27 17:26:34 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-27 17:26:34 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-27 17:26:34 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-27 17:26:35 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-27 17:26:35 - StateManager - INFO - WorkflowStateManager initialized
2025-06-27 17:26:35 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-CombineTextComponent-*************']
2025-06-27 17:26:35 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-27 17:26:35 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-27 17:26:35 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-CombineTextComponent-*************']
2025-06-27 17:26:35 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-27 17:26:35 - MCPToolExecutor - DEBUG - Set correlation ID to: 808deac0-5943-4173-b272-749330a67ffe
2025-06-27 17:26:35 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 808deac0-5943-4173-b272-749330a67ffe in tool_executor
2025-06-27 17:26:35 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-27 17:26:35 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-27 17:26:35 - NodeExecutor - DEBUG - Set correlation ID to: 808deac0-5943-4173-b272-749330a67ffe
2025-06-27 17:26:35 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 808deac0-5943-4173-b272-749330a67ffe in node_executor
2025-06-27 17:26:35 - AgentExecutor - DEBUG - Set correlation ID to: 808deac0-5943-4173-b272-749330a67ffe
2025-06-27 17:26:35 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 808deac0-5943-4173-b272-749330a67ffe in agent_executor
2025-06-27 17:26:35 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-27 17:26:35 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-27 17:26:35 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-27 17:26:35 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 808deac0-5943-4173-b272-749330a67ffe
2025-06-27 17:26:35 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 808deac0-5943-4173-b272-749330a67ffe
2025-06-27 17:26:35 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 808deac0-5943-4173-b272-749330a67ffe, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-27 17:26:35 - StateManager - INFO - Workflow initialized with initial transition: transition-CombineTextComponent-*************
2025-06-27 17:26:35 - StateManager - DEBUG - State: pending={'transition-CombineTextComponent-*************'}, waiting=set(), completed=set()
2025-06-27 17:26:35 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-CombineTextComponent-*************
2025-06-27 17:26:35 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-27 17:26:36 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:808deac0-5943-4173-b272-749330a67ffe'
2025-06-27 17:26:36 - RedisManager - DEBUG - Set key 'workflow_state:808deac0-5943-4173-b272-749330a67ffe' with TTL of 600 seconds
2025-06-27 17:26:36 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 808deac0-5943-4173-b272-749330a67ffe. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:36 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-27 17:26:36 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-27 17:26:36 - StateManager - INFO - Cleared 1 pending transitions: {'transition-CombineTextComponent-*************'}
2025-06-27 17:26:36 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-27 17:26:36 - StateManager - INFO - Terminated: False
2025-06-27 17:26:36 - StateManager - INFO - Pending transitions (0): []
2025-06-27 17:26:36 - StateManager - INFO - Waiting transitions (0): []
2025-06-27 17:26:36 - StateManager - INFO - Completed transitions (0): []
2025-06-27 17:26:36 - StateManager - INFO - Results stored for 0 transitions
2025-06-27 17:26:36 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-27 17:26:36 - StateManager - INFO - Workflow status: inactive
2025-06-27 17:26:36 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-27 17:26:36 - StateManager - INFO - Workflow status: inactive
2025-06-27 17:26:36 - StateManager - INFO - Workflow paused: False
2025-06-27 17:26:36 - StateManager - INFO - ==============================
2025-06-27 17:26:36 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-27 17:26:36 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 808deac0-5943-4173-b272-749330a67ffe):
2025-06-27 17:26:36 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 808deac0-5943-4173-b272-749330a67ffe, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-27 17:26:36 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=initial, execution_type=Components)
2025-06-27 17:26:36 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-27 17:26:36 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-27 17:26:36 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-27 17:26:36 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-27 17:26:36 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-27 17:26:36 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-27 17:26:36 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-27 17:26:36 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-27 17:26:36 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-27 17:26:36 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-27 17:26:36 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-27 17:26:36 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-27 17:26:36 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-27 17:26:36 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-27 17:26:36 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 4 fields (9 null/empty fields removed)
2025-06-27 17:26:36 - TransitionHandler - DEBUG - tool Parameters: {'main_input': '1', 'num_additional_inputs': '1', 'separator': '0', 'input_1': '0'}
2025-06-27 17:26:36 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': '1', 'num_additional_inputs': '1', 'separator': '0', 'input_1': '0'}
2025-06-27 17:26:36 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 808deac0-5943-4173-b272-749330a67ffe):
2025-06-27 17:26:36 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 808deac0-5943-4173-b272-749330a67ffe, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-27 17:26:36 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 7501e36b-3551-4f67-b59b-68b9e56d1aa0) using provided producer.
2025-06-27 17:26:36 - NodeExecutor - DEBUG - Added correlation_id 808deac0-5943-4173-b272-749330a67ffe to payload
2025-06-27 17:26:36 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-27 17:26:36 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': '1', 'num_additional_inputs': '1', 'separator': '0', 'input_1': '0'}, 'request_id': '7501e36b-3551-4f67-b59b-68b9e56d1aa0', 'correlation_id': '808deac0-5943-4173-b272-749330a67ffe', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-27 17:26:36 - NodeExecutor - DEBUG - Request 7501e36b-3551-4f67-b59b-68b9e56d1aa0 sent successfully using provided producer.
2025-06-27 17:26:36 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 7501e36b-3551-4f67-b59b-68b9e56d1aa0...
2025-06-27 17:26:37 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 966, corr_id: 808deac0-5943-4173-b272-749330a67ffe
2025-06-27 17:26:37 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=967
2025-06-27 17:26:37 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751025387, 'task_type': 'workflow', 'data': {'workflow_id': '50604f34-6f45-4b9d-a32c-d16691a2d80a', 'payload': {'user_dependent_fields': ['main_input'], 'user_payload_template': {'main_input': {'value': '1', 'transition_id': 'CombineTextComponent-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-27 17:26:37 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 50604f34-6f45-4b9d-a32c-d16691a2d80a
2025-06-27 17:26:37 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/50604f34-6f45-4b9d-a32c-d16691a2d80a
2025-06-27 17:26:38 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-27 17:26:38 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Untitled Workflow retrieved successfully",
  "workflow": {
    "id": "50604f34-6f45-4b9d-a32c-d16691a2d80a",
    "name": "Untitled Workflow",
    "description": "Untitled_Workflow",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/8a519087-837e-471a-96ad-f9372e5a95e3.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/760f412e-5db0-42f4-a077-4c570c061e6a.json",
    "start_nodes": [
      {
        "field": "main_input",
        "type": "string",
        "transition_id": "transition-CombineTextComponent-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-27T08:01:16.036804",
    "updated_at": "2025-06-27T11:56:21.455322",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      }
    ],
    "is_updated": true
  }
}
2025-06-27 17:26:38 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 50604f34-6f45-4b9d-a32c-d16691a2d80a - server_script_path is optional
2025-06-27 17:26:38 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-27 17:26:38 - StateManager - DEBUG - Using global database connections from initializer
2025-06-27 17:26:38 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-27 17:26:38 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-27 17:26:38 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-27 17:26:39 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-27 17:26:39 - StateManager - INFO - WorkflowStateManager initialized
2025-06-27 17:26:39 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-27 17:26:39 - StateManager - DEBUG - Using provided database connections
2025-06-27 17:26:39 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-27 17:26:39 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-27 17:26:39 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-27 17:26:40 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-27 17:26:40 - StateManager - INFO - WorkflowStateManager initialized
2025-06-27 17:26:40 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-CombineTextComponent-*************']
2025-06-27 17:26:40 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-27 17:26:40 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-27 17:26:40 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-CombineTextComponent-*************']
2025-06-27 17:26:40 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-27 17:26:40 - MCPToolExecutor - DEBUG - Set correlation ID to: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb
2025-06-27 17:26:40 - EnhancedWorkflowEngine - DEBUG - Set correlation_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb in tool_executor
2025-06-27 17:26:40 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-27 17:26:40 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-27 17:26:40 - NodeExecutor - DEBUG - Set correlation ID to: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb
2025-06-27 17:26:40 - EnhancedWorkflowEngine - DEBUG - Set correlation_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb in node_executor
2025-06-27 17:26:40 - AgentExecutor - DEBUG - Set correlation ID to: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb
2025-06-27 17:26:40 - EnhancedWorkflowEngine - DEBUG - Set correlation_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb in agent_executor
2025-06-27 17:26:40 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-27 17:26:40 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-27 17:26:40 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-27 17:26:40 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb
2025-06-27 17:26:40 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb
2025-06-27 17:26:40 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-27 17:26:40 - StateManager - INFO - Workflow initialized with initial transition: transition-CombineTextComponent-*************
2025-06-27 17:26:40 - StateManager - DEBUG - State: pending={'transition-CombineTextComponent-*************'}, waiting=set(), completed=set()
2025-06-27 17:26:40 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-CombineTextComponent-*************
2025-06-27 17:26:40 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-27 17:26:40 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb'
2025-06-27 17:26:40 - RedisManager - DEBUG - Set key 'workflow_state:cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb' with TTL of 600 seconds
2025-06-27 17:26:40 - StateManager - INFO - Workflow state saved to Redis for workflow ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:40 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-27 17:26:40 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-27 17:26:40 - StateManager - INFO - Cleared 1 pending transitions: {'transition-CombineTextComponent-*************'}
2025-06-27 17:26:40 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-27 17:26:40 - StateManager - INFO - Terminated: False
2025-06-27 17:26:40 - StateManager - INFO - Pending transitions (0): []
2025-06-27 17:26:40 - StateManager - INFO - Waiting transitions (0): []
2025-06-27 17:26:40 - StateManager - INFO - Completed transitions (0): []
2025-06-27 17:26:40 - StateManager - INFO - Results stored for 0 transitions
2025-06-27 17:26:40 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-27 17:26:40 - StateManager - INFO - Workflow status: inactive
2025-06-27 17:26:40 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-27 17:26:40 - StateManager - INFO - Workflow status: inactive
2025-06-27 17:26:40 - StateManager - INFO - Workflow paused: False
2025-06-27 17:26:40 - StateManager - INFO - ==============================
2025-06-27 17:26:40 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-27 17:26:40 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb):
2025-06-27 17:26:40 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-27 17:26:40 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=initial, execution_type=Components)
2025-06-27 17:26:40 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-27 17:26:40 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-27 17:26:40 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-27 17:26:40 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-27 17:26:40 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-27 17:26:40 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-27 17:26:40 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-27 17:26:40 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-27 17:26:40 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-27 17:26:40 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-27 17:26:40 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-27 17:26:40 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-27 17:26:40 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-27 17:26:40 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-27 17:26:40 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 4 fields (9 null/empty fields removed)
2025-06-27 17:26:40 - TransitionHandler - DEBUG - tool Parameters: {'main_input': '1', 'num_additional_inputs': '1', 'separator': '0', 'input_1': '0'}
2025-06-27 17:26:40 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': '1', 'num_additional_inputs': '1', 'separator': '0', 'input_1': '0'}
2025-06-27 17:26:40 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb):
2025-06-27 17:26:40 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-27 17:26:40 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 4d435d5d-55fb-421d-a8c7-48a96a90c20c) using provided producer.
2025-06-27 17:26:40 - NodeExecutor - DEBUG - Added correlation_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb to payload
2025-06-27 17:26:40 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-27 17:26:40 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': '1', 'num_additional_inputs': '1', 'separator': '0', 'input_1': '0'}, 'request_id': '4d435d5d-55fb-421d-a8c7-48a96a90c20c', 'correlation_id': 'cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-27 17:26:40 - NodeExecutor - DEBUG - Request 4d435d5d-55fb-421d-a8c7-48a96a90c20c sent successfully using provided producer.
2025-06-27 17:26:40 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 4d435d5d-55fb-421d-a8c7-48a96a90c20c...
2025-06-27 17:26:40 - NodeExecutor - DEBUG - Result consumer received message: Offset=745
2025-06-27 17:26:40 - NodeExecutor - DEBUG - Received valid result for request_id 7501e36b-3551-4f67-b59b-68b9e56d1aa0
2025-06-27 17:26:40 - NodeExecutor - INFO - Result received for request 7501e36b-3551-4f67-b59b-68b9e56d1aa0.
2025-06-27 17:26:40 - TransitionHandler - INFO - Execution result from Components executor: "100"
2025-06-27 17:26:40 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 808deac0-5943-4173-b272-749330a67ffe):
2025-06-27 17:26:40 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 808deac0-5943-4173-b272-749330a67ffe, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '100', 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-06-27 17:26:40 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '100'}, 'status': 'completed', 'timestamp': 1751025400.784516}}
2025-06-27 17:26:41 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-27 17:26:41 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-27 17:26:41 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:41 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-27 17:26:41 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************'}
2025-06-27 17:26:41 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-27 17:26:41 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-27 17:26:41 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-27 17:26:41 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-27 17:26:41 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-27 17:26:41 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-27 17:26:41 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-27 17:26:41 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-27 17:26:41 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 5.08 seconds
2025-06-27 17:26:41 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 808deac0-5943-4173-b272-749330a67ffe):
2025-06-27 17:26:41 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 808deac0-5943-4173-b272-749330a67ffe, response: {'result': 'Completed transition in 5.08 seconds', 'message': 'Transition completed in 5.08 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-06-27 17:26:41 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-06-27 17:26:41 - EnhancedWorkflowEngine - INFO - Transition transition-CombineTextComponent-************* completed successfully: 1 next transitions
2025-06-27 17:26:41 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-06-27 17:26:41 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-06-27 17:26:41 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-27 17:26:42 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:808deac0-5943-4173-b272-749330a67ffe'
2025-06-27 17:26:42 - RedisManager - DEBUG - Set key 'workflow_state:808deac0-5943-4173-b272-749330a67ffe' with TTL of 600 seconds
2025-06-27 17:26:42 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 808deac0-5943-4173-b272-749330a67ffe. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:42 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-27 17:26:42 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-27 17:26:42 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-27 17:26:42 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-27 17:26:42 - StateManager - INFO - Terminated: False
2025-06-27 17:26:42 - StateManager - INFO - Pending transitions (0): []
2025-06-27 17:26:42 - StateManager - INFO - Waiting transitions (0): []
2025-06-27 17:26:42 - StateManager - INFO - Completed transitions (1): ['transition-CombineTextComponent-*************']
2025-06-27 17:26:42 - StateManager - INFO - Results stored for 1 transitions
2025-06-27 17:26:42 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-27 17:26:42 - StateManager - INFO - Workflow status: inactive
2025-06-27 17:26:42 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-27 17:26:42 - StateManager - INFO - Workflow status: inactive
2025-06-27 17:26:42 - StateManager - INFO - Workflow paused: False
2025-06-27 17:26:42 - StateManager - INFO - ==============================
2025-06-27 17:26:42 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-27 17:26:42 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 808deac0-5943-4173-b272-749330a67ffe):
2025-06-27 17:26:42 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 808deac0-5943-4173-b272-749330a67ffe, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-06-27 17:26:42 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-06-27 17:26:42 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-27 17:26:42 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-27 17:26:42 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-27 17:26:42 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-27 17:26:42 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-27 17:26:43 - StateManager - DEBUG - Retrieved result for transition transition-CombineTextComponent-************* from Redis
2025-06-27 17:26:43 - StateManager - DEBUG - Detected wrapped result structure for transition transition-CombineTextComponent-*************, extracting data
2025-06-27 17:26:43 - StateManager - DEBUG - Extracted double-nested result data for transition transition-CombineTextComponent-*************
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-CombineTextComponent-*************
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-CombineTextComponent-************* (total: 1)
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': '100'}
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: 100
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - Found result.result: 100 (type: <class 'str'>)
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-27 17:26:43 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-27 17:26:43 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': '100'}
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: 100
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - Found result.result: 100 (type: <class 'str'>)
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - ✅ Handle mapping success: result → start via path 'result': 100
2025-06-27 17:26:43 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-27 17:26:43 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-06-27 17:26:43 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-06-27 17:26:43 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 105
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 3
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = True
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = combine_text
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = True
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = continue
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-06-27 17:26:43 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '100', 'source_type': 'number_range', 'batch_size': '1', 'end': '105', 'step': '3', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'combine_text', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-27 17:26:43 - TransitionHandler - DEBUG - tool Parameters: {'start': '100', 'source_type': 'number_range', 'batch_size': '1', 'end': '105', 'step': '3', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'combine_text', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-27 17:26:43 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '100', 'source_type': 'number_range', 'batch_size': '1', 'end': '105', 'step': '3', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'combine_text', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-27 17:26:43 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 808deac0-5943-4173-b272-749330a67ffe):
2025-06-27 17:26:43 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 808deac0-5943-4173-b272-749330a67ffe, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-27 17:26:43 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-27 17:26:43 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-27 17:26:43 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-27 17:26:43 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: 100
2025-06-27 17:26:43 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-27 17:26:44 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-27 17:26:44 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:44 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-27 17:26:44 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0'}
2025-06-27 17:26:44 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 100
2025-06-27 17:26:44 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-27 17:26:45 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-27 17:26:45 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:45 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-27 17:26:45 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-CombineTextComponent-*************', 'loop_iteration_0'}
2025-06-27 17:26:45 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 100, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 216688.529565625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-27 17:26:45 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-27 17:26:45 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-27 17:26:45 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:45 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-27 17:26:45 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-LoopNode-*************', 'current_iteration', 'transition-CombineTextComponent-*************', 'loop_iteration_0'}
2025-06-27 17:26:45 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-27 17:26:45 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 808deac0-5943-4173-b272-749330a67ffe):
2025-06-27 17:26:45 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 808deac0-5943-4173-b272-749330a67ffe, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-27 17:26:45 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-27 17:26:45 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-27 17:26:45 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-27 17:26:45 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-27 17:26:45 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-27 17:26:46 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-27 17:26:46 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-27 17:26:46 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 100, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 216688.529565625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-27 17:26:46 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-27 17:26:46 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 100, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 216688.529565625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 100
2025-06-27 17:26:46 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-27 17:26:46 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Filtering out field 'num_additional_inputs' with null/empty value: None
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: None
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: None
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: None
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: None
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: None
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: None
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: None
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: None
2025-06-27 17:26:46 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: None
2025-06-27 17:26:46 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 1 fields (12 null/empty fields removed)
2025-06-27 17:26:46 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 100}
2025-06-27 17:26:46 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 100}
2025-06-27 17:26:46 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 100}
2025-06-27 17:26:46 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 808deac0-5943-4173-b272-749330a67ffe):
2025-06-27 17:26:46 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 808deac0-5943-4173-b272-749330a67ffe, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-27 17:26:46 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 0165f149-02b0-475b-8fed-1ba44f71d5d4) using provided producer.
2025-06-27 17:26:46 - NodeExecutor - DEBUG - Added correlation_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb to payload
2025-06-27 17:26:46 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-27 17:26:46 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 100}, 'request_id': '0165f149-02b0-475b-8fed-1ba44f71d5d4', 'correlation_id': 'cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-27 17:26:46 - NodeExecutor - DEBUG - Request 0165f149-02b0-475b-8fed-1ba44f71d5d4 sent successfully using provided producer.
2025-06-27 17:26:46 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 0165f149-02b0-475b-8fed-1ba44f71d5d4...
2025-06-27 17:26:46 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 967, corr_id: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb
2025-06-27 17:26:46 - NodeExecutor - DEBUG - Result consumer received message: Offset=746
2025-06-27 17:26:46 - NodeExecutor - DEBUG - Received valid result for request_id 4d435d5d-55fb-421d-a8c7-48a96a90c20c
2025-06-27 17:26:46 - NodeExecutor - INFO - Result received for request 4d435d5d-55fb-421d-a8c7-48a96a90c20c.
2025-06-27 17:26:46 - TransitionHandler - INFO - Execution result from Components executor: "100"
2025-06-27 17:26:46 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb):
2025-06-27 17:26:46 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '100', 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-06-27 17:26:46 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '100'}, 'status': 'completed', 'timestamp': 1751025406.8072782}}
2025-06-27 17:26:47 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-27 17:26:47 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-27 17:26:47 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:47 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-27 17:26:47 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************'}
2025-06-27 17:26:47 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-27 17:26:47 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-27 17:26:47 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-27 17:26:47 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-27 17:26:47 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-27 17:26:47 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-27 17:26:47 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-27 17:26:47 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-27 17:26:47 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 6.85 seconds
2025-06-27 17:26:47 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb):
2025-06-27 17:26:47 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb, response: {'result': 'Completed transition in 6.85 seconds', 'message': 'Transition completed in 6.85 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-06-27 17:26:47 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-06-27 17:26:47 - EnhancedWorkflowEngine - INFO - Transition transition-CombineTextComponent-************* completed successfully: 1 next transitions
2025-06-27 17:26:47 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-06-27 17:26:47 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-06-27 17:26:47 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-27 17:26:48 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb'
2025-06-27 17:26:48 - RedisManager - DEBUG - Set key 'workflow_state:cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb' with TTL of 600 seconds
2025-06-27 17:26:48 - StateManager - INFO - Workflow state saved to Redis for workflow ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:48 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-27 17:26:48 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-27 17:26:48 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-27 17:26:48 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-27 17:26:48 - StateManager - INFO - Terminated: False
2025-06-27 17:26:48 - StateManager - INFO - Pending transitions (0): []
2025-06-27 17:26:48 - StateManager - INFO - Waiting transitions (0): []
2025-06-27 17:26:48 - StateManager - INFO - Completed transitions (1): ['transition-CombineTextComponent-*************']
2025-06-27 17:26:48 - StateManager - INFO - Results stored for 1 transitions
2025-06-27 17:26:48 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-27 17:26:48 - StateManager - INFO - Workflow status: inactive
2025-06-27 17:26:48 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-27 17:26:48 - StateManager - INFO - Workflow status: inactive
2025-06-27 17:26:48 - StateManager - INFO - Workflow paused: False
2025-06-27 17:26:48 - StateManager - INFO - ==============================
2025-06-27 17:26:48 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-27 17:26:48 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb):
2025-06-27 17:26:48 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-06-27 17:26:48 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-06-27 17:26:48 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-27 17:26:48 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-27 17:26:48 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-27 17:26:48 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-27 17:26:48 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-27 17:26:49 - StateManager - DEBUG - Retrieved result for transition transition-CombineTextComponent-************* from Redis
2025-06-27 17:26:49 - StateManager - DEBUG - Detected wrapped result structure for transition transition-CombineTextComponent-*************, extracting data
2025-06-27 17:26:49 - StateManager - DEBUG - Extracted double-nested result data for transition transition-CombineTextComponent-*************
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-CombineTextComponent-*************
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-CombineTextComponent-************* (total: 1)
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': '100'}
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: 100
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - Found result.result: 100 (type: <class 'str'>)
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-27 17:26:49 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-27 17:26:49 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': '100'}
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: 100
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - Found result.result: 100 (type: <class 'str'>)
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - ✅ Handle mapping success: result → start via path 'result': 100
2025-06-27 17:26:49 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-27 17:26:49 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-06-27 17:26:49 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-06-27 17:26:49 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 105
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 3
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = True
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = combine_text
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = True
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = continue
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-06-27 17:26:49 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '100', 'source_type': 'number_range', 'batch_size': '1', 'end': '105', 'step': '3', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'combine_text', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-27 17:26:49 - TransitionHandler - DEBUG - tool Parameters: {'start': '100', 'source_type': 'number_range', 'batch_size': '1', 'end': '105', 'step': '3', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'combine_text', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-27 17:26:49 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '100', 'source_type': 'number_range', 'batch_size': '1', 'end': '105', 'step': '3', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'combine_text', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-27 17:26:49 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb):
2025-06-27 17:26:49 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-27 17:26:49 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-27 17:26:49 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-27 17:26:49 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-27 17:26:49 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: 100
2025-06-27 17:26:49 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-27 17:26:50 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-27 17:26:50 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:50 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-27 17:26:50 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0'}
2025-06-27 17:26:50 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 100
2025-06-27 17:26:50 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-27 17:26:50 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-27 17:26:50 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:50 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-27 17:26:50 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-CombineTextComponent-*************', 'loop_iteration_0'}
2025-06-27 17:26:50 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 100, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 216694.239160166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-27 17:26:51 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-27 17:26:51 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-27 17:26:51 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:51 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-27 17:26:51 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-LoopNode-*************', 'current_iteration', 'transition-CombineTextComponent-*************', 'loop_iteration_0'}
2025-06-27 17:26:51 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-27 17:26:51 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb):
2025-06-27 17:26:51 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-27 17:26:51 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-27 17:26:51 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-27 17:26:51 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-27 17:26:51 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-27 17:26:51 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-27 17:26:52 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-27 17:26:52 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-27 17:26:52 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 100, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 216694.239160166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-27 17:26:52 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-27 17:26:52 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 100, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 216694.239160166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 100
2025-06-27 17:26:52 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-27 17:26:52 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Filtering out field 'num_additional_inputs' with null/empty value: None
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: None
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: None
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: None
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: None
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: None
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: None
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: None
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: None
2025-06-27 17:26:52 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: None
2025-06-27 17:26:52 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 1 fields (12 null/empty fields removed)
2025-06-27 17:26:52 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 100}
2025-06-27 17:26:52 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 100}
2025-06-27 17:26:52 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 100}
2025-06-27 17:26:52 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb):
2025-06-27 17:26:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-27 17:26:52 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 236cf0e2-481a-4762-a363-3f2167262427) using provided producer.
2025-06-27 17:26:52 - NodeExecutor - DEBUG - Added correlation_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb to payload
2025-06-27 17:26:52 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-27 17:26:52 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 100}, 'request_id': '236cf0e2-481a-4762-a363-3f2167262427', 'correlation_id': 'cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-27 17:26:52 - NodeExecutor - DEBUG - Request 236cf0e2-481a-4762-a363-3f2167262427 sent successfully using provided producer.
2025-06-27 17:26:52 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 236cf0e2-481a-4762-a363-3f2167262427...
2025-06-27 17:26:52 - NodeExecutor - DEBUG - Result consumer received message: Offset=747
2025-06-27 17:26:52 - NodeExecutor - DEBUG - Received valid result for request_id 0165f149-02b0-475b-8fed-1ba44f71d5d4
2025-06-27 17:26:52 - NodeExecutor - INFO - Result received for request 0165f149-02b0-475b-8fed-1ba44f71d5d4.
2025-06-27 17:26:52 - TransitionHandler - INFO - Execution result from Components executor: "100"
2025-06-27 17:26:52 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 808deac0-5943-4173-b272-749330a67ffe):
2025-06-27 17:26:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 808deac0-5943-4173-b272-749330a67ffe, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '100', 'status': 'completed', 'sequence': 8, 'workflow_status': 'running', 'approval_required': False}
2025-06-27 17:26:52 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '100'}, 'status': 'completed', 'timestamp': 1751025412.516503}}
2025-06-27 17:26:53 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-27 17:26:53 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-27 17:26:53 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:53 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-27 17:26:53 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************', 'current_iteration', 'loop_iteration_0'}
2025-06-27 17:26:53 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-27 17:26:53 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-27 17:26:53 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-27 17:26:53 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-27 17:26:53 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-27 17:26:53 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-27 17:26:53 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-27 17:26:53 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-27 17:26:53 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-27 17:26:53 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 7.36 seconds
2025-06-27 17:26:53 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 808deac0-5943-4173-b272-749330a67ffe):
2025-06-27 17:26:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 808deac0-5943-4173-b272-749330a67ffe, response: {'result': 'Completed transition in 7.36 seconds', 'message': 'Transition completed in 7.36 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 9, 'workflow_status': 'running'}
2025-06-27 17:26:53 - NodeExecutor - DEBUG - Result consumer received message: Offset=748
2025-06-27 17:26:53 - NodeExecutor - DEBUG - Received valid result for request_id 236cf0e2-481a-4762-a363-3f2167262427
2025-06-27 17:26:53 - NodeExecutor - INFO - Result received for request 236cf0e2-481a-4762-a363-3f2167262427.
2025-06-27 17:26:53 - TransitionHandler - INFO - Execution result from Components executor: "100"
2025-06-27 17:26:53 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb):
2025-06-27 17:26:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '100', 'status': 'completed', 'sequence': 8, 'workflow_status': 'running', 'approval_required': False}
2025-06-27 17:26:53 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '100'}, 'status': 'completed', 'timestamp': 1751025413.606432}}
2025-06-27 17:26:54 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-27 17:26:54 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-27 17:26:54 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 17:26:54 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-27 17:26:54 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************', 'current_iteration', 'loop_iteration_0'}
2025-06-27 17:26:54 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-27 17:26:54 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-27 17:26:54 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-27 17:26:54 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-27 17:26:54 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-27 17:26:54 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-27 17:26:54 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-27 17:26:54 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-27 17:26:54 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-27 17:26:54 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.75 seconds
2025-06-27 17:26:54 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb):
2025-06-27 17:26:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb, response: {'result': 'Completed transition in 2.75 seconds', 'message': 'Transition completed in 2.75 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 9, 'workflow_status': 'running'}
2025-06-27 17:27:01 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-27 17:27:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-27 17:27:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-27 17:27:02 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-27 17:28:01 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-27 17:28:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-27 17:28:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-27 17:28:02 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-27 17:29:01 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-27 17:29:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-27 17:29:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-27 17:29:02 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-27 17:29:16 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-27 17:29:16 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-27 17:29:16 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-27 17:29:16 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-27 17:29:16 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-27 17:29:16 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-27 17:29:16 - EnhancedWorkflowEngine - WARNING - Workflow 808deac0-5943-4173-b272-749330a67ffe execution was cancelled!
2025-06-27 17:29:16 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-27 17:29:16 - EnhancedWorkflowEngine - WARNING - Workflow cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb execution was cancelled!
2025-06-27 17:29:16 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-27 17:29:16 - KafkaWorkflowConsumer - WARNING - Workflow execution for '50604f34-6f45-4b9d-a32c-d16691a2d80a' was cancelled
2025-06-27 17:29:16 - KafkaWorkflowConsumer - INFO - Workflow '50604f34-6f45-4b9d-a32c-d16691a2d80a' final status: cancelled, result: Workflow '50604f34-6f45-4b9d-a32c-d16691a2d80a' execution was cancelled
2025-06-27 17:29:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 808deac0-5943-4173-b272-749330a67ffe, response: {'status': 'Workflow Cancelled', 'result': "Workflow '50604f34-6f45-4b9d-a32c-d16691a2d80a' execution was cancelled", 'workflow_status': 'cancelled'}
2025-06-27 17:29:16 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 808deac0-5943-4173-b272-749330a67ffe 
2025-06-27 17:29:16 - KafkaWorkflowConsumer - WARNING - Workflow execution for '50604f34-6f45-4b9d-a32c-d16691a2d80a' was cancelled
2025-06-27 17:29:16 - KafkaWorkflowConsumer - INFO - Workflow '50604f34-6f45-4b9d-a32c-d16691a2d80a' final status: cancelled, result: Workflow '50604f34-6f45-4b9d-a32c-d16691a2d80a' execution was cancelled
2025-06-27 17:29:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb, response: {'status': 'Workflow Cancelled', 'result': "Workflow '50604f34-6f45-4b9d-a32c-d16691a2d80a' execution was cancelled", 'workflow_status': 'cancelled'}
2025-06-27 17:29:16 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: cd47ddd6-36eb-41a3-8ac1-b2e2de3a07fb 
2025-06-27 17:29:16 - Main - ERROR - Shutting down due to keyboard interrupt...
