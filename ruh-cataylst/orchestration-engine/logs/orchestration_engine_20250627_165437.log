2025-06-27 16:54:37 - Main - INFO - Starting Server
2025-06-27 16:54:37 - Main - INFO - Connection at: **************:9092
2025-06-27 16:54:37 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-27 16:54:37 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-27 16:54:37 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-27 16:54:37 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-27 16:54:37 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-27 16:54:38 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-27 16:54:38 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-27 16:54:40 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
2025-06-27 16:54:42 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-27 16:54:42 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-27 16:54:44 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-27 16:54:45 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-27 16:54:45 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-27 16:54:46 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-27 16:54:46 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-27 16:54:48 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-27 16:54:48 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-27 16:54:48 - RedisEventListener - INFO - Redis event listener started
2025-06-27 16:54:48 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-27 16:54:48 - StateManager - DEBUG - Using provided database connections
2025-06-27 16:54:48 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-27 16:54:48 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-27 16:54:48 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-27 16:54:49 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-27 16:54:49 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-27 16:54:49 - StateManager - INFO - WorkflowStateManager initialized
2025-06-27 16:54:49 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-27 16:54:49 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-27 16:54:49 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-27 16:54:49 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-27 16:54:52 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-27 16:54:52 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-27 16:54:52 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-27 16:55:05 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:loop_iteration_0', 'data': b'expired'}
2025-06-27 16:55:05 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:loop_iteration_0'
2025-06-27 16:55:05 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:loop_iteration_0
2025-06-27 16:55:05 - RedisEventListener - DEBUG - Extracted key: result:loop_iteration_0
2025-06-27 16:55:05 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-27 16:55:05 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-27 16:55:05 - RedisEventListener - INFO - Detected expired event for result of transition: loop_iteration_0
2025-06-27 16:55:05 - RedisEventListener - INFO - Archiving result for transition: loop_iteration_0
2025-06-27 16:55:05 - StateManager - DEBUG - Attempting to archive result for transition loop_iteration_0
2025-06-27 16:55:06 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-27 16:55:06 - StateManager - DEBUG - Provided result: False
2025-06-27 16:55:06 - StateManager - DEBUG - Trying to get result from Redis for transition loop_iteration_0
2025-06-27 16:55:07 - StateManager - DEBUG - No result found in Redis for transition loop_iteration_0
2025-06-27 16:55:07 - StateManager - DEBUG - Trying to get result from memory for transition loop_iteration_0
2025-06-27 16:55:07 - StateManager - DEBUG - No result found in memory for transition loop_iteration_0
2025-06-27 16:55:07 - StateManager - DEBUG - Available transition results in memory: []
2025-06-27 16:55:07 - StateManager - DEBUG - No result found to archive for loop iteration transition loop_iteration_0 (this is expected for temporary loop data)
2025-06-27 16:55:07 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:current_iteration', 'data': b'expired'}
2025-06-27 16:55:07 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:current_iteration'
2025-06-27 16:55:07 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:current_iteration
2025-06-27 16:55:07 - RedisEventListener - DEBUG - Extracted key: result:current_iteration
2025-06-27 16:55:07 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-27 16:55:07 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-27 16:55:07 - RedisEventListener - INFO - Detected expired event for result of transition: current_iteration
2025-06-27 16:55:07 - RedisEventListener - INFO - Archiving result for transition: current_iteration
2025-06-27 16:55:07 - StateManager - DEBUG - Attempting to archive result for transition current_iteration
2025-06-27 16:55:08 - StateManager - DEBUG - Provided result: False
2025-06-27 16:55:08 - StateManager - DEBUG - Trying to get result from Redis for transition current_iteration
2025-06-27 16:55:08 - StateManager - DEBUG - No result found in Redis for transition current_iteration
2025-06-27 16:55:08 - StateManager - DEBUG - Trying to get result from memory for transition current_iteration
2025-06-27 16:55:08 - StateManager - DEBUG - No result found in memory for transition current_iteration
2025-06-27 16:55:08 - StateManager - DEBUG - Available transition results in memory: []
2025-06-27 16:55:08 - StateManager - DEBUG - No result found to archive for loop iteration transition current_iteration (this is expected for temporary loop data)
2025-06-27 16:55:12 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-27 16:55:12 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-27 16:55:12 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-27 16:55:18 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-27 16:55:18 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-27 16:55:18 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-27 16:55:25 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-27 16:55:25 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-27 16:55:25 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=965
2025-06-27 16:55:25 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751023505, 'task_type': 'workflow', 'data': {'workflow_id': '50604f34-6f45-4b9d-a32c-d16691a2d80a', 'payload': {'user_dependent_fields': ['main_input'], 'user_payload_template': {'main_input': {'value': '1', 'transition_id': 'CombineTextComponent-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-27 16:55:25 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 50604f34-6f45-4b9d-a32c-d16691a2d80a
2025-06-27 16:55:25 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/50604f34-6f45-4b9d-a32c-d16691a2d80a
2025-06-27 16:55:25 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-27 16:55:25 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Untitled Workflow retrieved successfully",
  "workflow": {
    "id": "50604f34-6f45-4b9d-a32c-d16691a2d80a",
    "name": "Untitled Workflow",
    "description": "Untitled_Workflow",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/5a7001ac-47a4-4df8-8af6-9ccd5d1adbf1.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/26ab9400-bd64-4a6d-a0b0-450d8475c0ef.json",
    "start_nodes": [
      {
        "field": "main_input",
        "type": "string",
        "transition_id": "transition-CombineTextComponent-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-27T08:01:16.036804",
    "updated_at": "2025-06-27T11:25:03.499055",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      }
    ],
    "is_updated": true
  }
}
2025-06-27 16:55:26 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 50604f34-6f45-4b9d-a32c-d16691a2d80a - server_script_path is optional
2025-06-27 16:55:26 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-27 16:55:26 - StateManager - DEBUG - Using global database connections from initializer
2025-06-27 16:55:26 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-27 16:55:26 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-27 16:55:26 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-27 16:55:27 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-27 16:55:27 - StateManager - INFO - WorkflowStateManager initialized
2025-06-27 16:55:27 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-27 16:55:27 - StateManager - DEBUG - Using provided database connections
2025-06-27 16:55:27 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-27 16:55:27 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-27 16:55:27 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-27 16:55:28 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-27 16:55:28 - StateManager - INFO - WorkflowStateManager initialized
2025-06-27 16:55:28 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-CombineTextComponent-*************']
2025-06-27 16:55:28 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-27 16:55:28 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-27 16:55:28 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-CombineTextComponent-*************']
2025-06-27 16:55:28 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-27 16:55:28 - MCPToolExecutor - DEBUG - Set correlation ID to: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5
2025-06-27 16:55:28 - EnhancedWorkflowEngine - DEBUG - Set correlation_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5 in tool_executor
2025-06-27 16:55:28 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-27 16:55:28 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-27 16:55:28 - NodeExecutor - DEBUG - Set correlation ID to: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5
2025-06-27 16:55:28 - EnhancedWorkflowEngine - DEBUG - Set correlation_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5 in node_executor
2025-06-27 16:55:28 - AgentExecutor - DEBUG - Set correlation ID to: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5
2025-06-27 16:55:28 - EnhancedWorkflowEngine - DEBUG - Set correlation_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5 in agent_executor
2025-06-27 16:55:28 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-27 16:55:28 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-27 16:55:28 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-27 16:55:28 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5
2025-06-27 16:55:28 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5
2025-06-27 16:55:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-27 16:55:28 - StateManager - INFO - Workflow initialized with initial transition: transition-CombineTextComponent-*************
2025-06-27 16:55:28 - StateManager - DEBUG - State: pending={'transition-CombineTextComponent-*************'}, waiting=set(), completed=set()
2025-06-27 16:55:28 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-CombineTextComponent-*************
2025-06-27 16:55:28 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-27 16:55:28 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5'
2025-06-27 16:55:29 - RedisManager - DEBUG - Set key 'workflow_state:cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5' with TTL of 600 seconds
2025-06-27 16:55:29 - StateManager - INFO - Workflow state saved to Redis for workflow ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 16:55:29 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-27 16:55:29 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-27 16:55:29 - StateManager - INFO - Cleared 1 pending transitions: {'transition-CombineTextComponent-*************'}
2025-06-27 16:55:29 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-27 16:55:29 - StateManager - INFO - Terminated: False
2025-06-27 16:55:29 - StateManager - INFO - Pending transitions (0): []
2025-06-27 16:55:29 - StateManager - INFO - Waiting transitions (0): []
2025-06-27 16:55:29 - StateManager - INFO - Completed transitions (0): []
2025-06-27 16:55:29 - StateManager - INFO - Results stored for 0 transitions
2025-06-27 16:55:29 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-27 16:55:29 - StateManager - INFO - Workflow status: inactive
2025-06-27 16:55:29 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-27 16:55:29 - StateManager - INFO - Workflow status: inactive
2025-06-27 16:55:29 - StateManager - INFO - Workflow paused: False
2025-06-27 16:55:29 - StateManager - INFO - ==============================
2025-06-27 16:55:29 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-27 16:55:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5):
2025-06-27 16:55:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-27 16:55:29 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=initial, execution_type=Components)
2025-06-27 16:55:29 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-27 16:55:29 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-27 16:55:29 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-27 16:55:29 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-27 16:55:29 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-27 16:55:29 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-27 16:55:29 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-27 16:55:29 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-27 16:55:29 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-27 16:55:29 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-27 16:55:29 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-27 16:55:29 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-27 16:55:29 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-27 16:55:29 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-27 16:55:29 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 4 fields (9 null/empty fields removed)
2025-06-27 16:55:29 - TransitionHandler - DEBUG - tool Parameters: {'main_input': '1', 'num_additional_inputs': '1', 'separator': '0', 'input_1': '0'}
2025-06-27 16:55:29 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': '1', 'num_additional_inputs': '1', 'separator': '0', 'input_1': '0'}
2025-06-27 16:55:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5):
2025-06-27 16:55:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-27 16:55:29 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 5ef4856b-8a4a-40be-bed3-7430f9c15802) using provided producer.
2025-06-27 16:55:29 - NodeExecutor - DEBUG - Added correlation_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5 to payload
2025-06-27 16:55:29 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-27 16:55:29 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': '1', 'num_additional_inputs': '1', 'separator': '0', 'input_1': '0'}, 'request_id': '5ef4856b-8a4a-40be-bed3-7430f9c15802', 'correlation_id': 'cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-27 16:55:29 - NodeExecutor - DEBUG - Request 5ef4856b-8a4a-40be-bed3-7430f9c15802 sent successfully using provided producer.
2025-06-27 16:55:29 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 5ef4856b-8a4a-40be-bed3-7430f9c15802...
2025-06-27 16:55:29 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 965, corr_id: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5
2025-06-27 16:55:30 - NodeExecutor - DEBUG - Result consumer received message: Offset=743
2025-06-27 16:55:30 - NodeExecutor - DEBUG - Received valid result for request_id 5ef4856b-8a4a-40be-bed3-7430f9c15802
2025-06-27 16:55:30 - NodeExecutor - INFO - Result received for request 5ef4856b-8a4a-40be-bed3-7430f9c15802.
2025-06-27 16:55:30 - TransitionHandler - INFO - Execution result from Components executor: "100"
2025-06-27 16:55:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5):
2025-06-27 16:55:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '100', 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-06-27 16:55:30 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '100'}, 'status': 'completed', 'timestamp': 1751023530.244812}}
2025-06-27 16:55:30 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-27 16:55:31 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-27 16:55:31 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 16:55:31 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-27 16:55:31 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************'}
2025-06-27 16:55:31 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-27 16:55:31 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-27 16:55:31 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-27 16:55:31 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-27 16:55:31 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-27 16:55:31 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-27 16:55:31 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-27 16:55:31 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-27 16:55:31 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 1.87 seconds
2025-06-27 16:55:31 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5):
2025-06-27 16:55:31 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5, response: {'result': 'Completed transition in 1.87 seconds', 'message': 'Transition completed in 1.87 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-06-27 16:55:31 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-06-27 16:55:31 - EnhancedWorkflowEngine - INFO - Transition transition-CombineTextComponent-************* completed successfully: 1 next transitions
2025-06-27 16:55:31 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-06-27 16:55:31 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-06-27 16:55:31 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-27 16:55:31 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5'
2025-06-27 16:55:31 - RedisManager - DEBUG - Set key 'workflow_state:cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5' with TTL of 600 seconds
2025-06-27 16:55:31 - StateManager - INFO - Workflow state saved to Redis for workflow ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 16:55:31 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-27 16:55:31 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-27 16:55:31 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-27 16:55:31 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-27 16:55:31 - StateManager - INFO - Terminated: False
2025-06-27 16:55:31 - StateManager - INFO - Pending transitions (0): []
2025-06-27 16:55:31 - StateManager - INFO - Waiting transitions (0): []
2025-06-27 16:55:31 - StateManager - INFO - Completed transitions (1): ['transition-CombineTextComponent-*************']
2025-06-27 16:55:31 - StateManager - INFO - Results stored for 1 transitions
2025-06-27 16:55:31 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-27 16:55:31 - StateManager - INFO - Workflow status: inactive
2025-06-27 16:55:31 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-27 16:55:31 - StateManager - INFO - Workflow status: inactive
2025-06-27 16:55:31 - StateManager - INFO - Workflow paused: False
2025-06-27 16:55:31 - StateManager - INFO - ==============================
2025-06-27 16:55:31 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-27 16:55:31 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5):
2025-06-27 16:55:31 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-06-27 16:55:31 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-06-27 16:55:31 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-27 16:55:31 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-27 16:55:31 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-27 16:55:31 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-27 16:55:31 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-27 16:55:32 - StateManager - DEBUG - Retrieved result for transition transition-CombineTextComponent-************* from Redis
2025-06-27 16:55:32 - StateManager - DEBUG - Detected wrapped result structure for transition transition-CombineTextComponent-*************, extracting data
2025-06-27 16:55:32 - StateManager - DEBUG - Extracted double-nested result data for transition transition-CombineTextComponent-*************
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-CombineTextComponent-*************
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-CombineTextComponent-************* (total: 1)
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': '100'}
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: 100
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - Found result.result: 100 (type: <class 'str'>)
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-27 16:55:32 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-27 16:55:32 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': '100'}
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: 100
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - Found result.result: 100 (type: <class 'str'>)
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - ✅ Handle mapping success: result → start via path 'result': 100
2025-06-27 16:55:32 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-27 16:55:32 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-06-27 16:55:32 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-06-27 16:55:32 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 105
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 3
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = True
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = combine_text
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = True
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = continue
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-06-27 16:55:32 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '100', 'source_type': 'number_range', 'batch_size': '1', 'end': '105', 'step': '3', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'combine_text', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-27 16:55:32 - TransitionHandler - DEBUG - tool Parameters: {'start': '100', 'source_type': 'number_range', 'batch_size': '1', 'end': '105', 'step': '3', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'combine_text', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-27 16:55:32 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '100', 'source_type': 'number_range', 'batch_size': '1', 'end': '105', 'step': '3', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'combine_text', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-27 16:55:32 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5):
2025-06-27 16:55:32 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-27 16:55:32 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-27 16:55:32 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-27 16:55:32 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-27 16:55:32 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: 100
2025-06-27 16:55:32 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-27 16:55:33 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-27 16:55:33 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 16:55:33 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-27 16:55:33 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-CombineTextComponent-*************'}
2025-06-27 16:55:33 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 100
2025-06-27 16:55:33 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-27 16:55:34 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-27 16:55:34 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 16:55:34 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-27 16:55:34 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-CombineTextComponent-*************', 'current_iteration'}
2025-06-27 16:55:34 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 100, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 214817.516667708, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-27 16:55:34 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-27 16:55:34 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-27 16:55:34 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 16:55:34 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-27 16:55:34 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-27 16:55:34 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-27 16:55:34 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5):
2025-06-27 16:55:34 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-27 16:55:34 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-27 16:55:34 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-27 16:55:34 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-27 16:55:34 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-27 16:55:34 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-27 16:55:35 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-27 16:55:35 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-27 16:55:35 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 100, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 214817.516667708, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-27 16:55:35 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-27 16:55:35 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 100, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 214817.516667708, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 100
2025-06-27 16:55:35 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-27 16:55:35 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Filtering out field 'num_additional_inputs' with null/empty value: None
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: None
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: None
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: None
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: None
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: None
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: None
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: None
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: None
2025-06-27 16:55:35 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: None
2025-06-27 16:55:35 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 1 fields (12 null/empty fields removed)
2025-06-27 16:55:35 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 100}
2025-06-27 16:55:35 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 100}
2025-06-27 16:55:35 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 100}
2025-06-27 16:55:35 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5):
2025-06-27 16:55:35 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-27 16:55:35 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: c3e2c98b-191c-44ee-b0a1-17ab60c22a37) using provided producer.
2025-06-27 16:55:35 - NodeExecutor - DEBUG - Added correlation_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5 to payload
2025-06-27 16:55:35 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-27 16:55:35 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 100}, 'request_id': 'c3e2c98b-191c-44ee-b0a1-17ab60c22a37', 'correlation_id': 'cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-27 16:55:35 - NodeExecutor - DEBUG - Request c3e2c98b-191c-44ee-b0a1-17ab60c22a37 sent successfully using provided producer.
2025-06-27 16:55:35 - NodeExecutor - DEBUG - Waiting indefinitely for result for request c3e2c98b-191c-44ee-b0a1-17ab60c22a37...
2025-06-27 16:55:36 - NodeExecutor - DEBUG - Result consumer received message: Offset=744
2025-06-27 16:55:36 - NodeExecutor - DEBUG - Received valid result for request_id c3e2c98b-191c-44ee-b0a1-17ab60c22a37
2025-06-27 16:55:36 - NodeExecutor - INFO - Result received for request c3e2c98b-191c-44ee-b0a1-17ab60c22a37.
2025-06-27 16:55:36 - TransitionHandler - INFO - Execution result from Components executor: "100"
2025-06-27 16:55:36 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5):
2025-06-27 16:55:36 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '100', 'status': 'completed', 'sequence': 8, 'workflow_status': 'running', 'approval_required': False}
2025-06-27 16:55:36 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '100'}, 'status': 'completed', 'timestamp': 1751023536.5109901}}
2025-06-27 16:55:37 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-27 16:55:37 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-27 16:55:37 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-27 16:55:37 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-27 16:55:37 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-CombineTextComponent-*************', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-27 16:55:37 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-27 16:55:37 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-27 16:55:37 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-27 16:55:37 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-27 16:55:37 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-27 16:55:37 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-27 16:55:37 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-27 16:55:37 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-27 16:55:37 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-27 16:55:37 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.32 seconds
2025-06-27 16:55:37 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5):
2025-06-27 16:55:37 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5, response: {'result': 'Completed transition in 2.32 seconds', 'message': 'Transition completed in 2.32 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 9, 'workflow_status': 'running'}
2025-06-27 16:55:45 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-27 16:55:45 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-27 16:55:45 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-27 16:55:45 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-27 16:55:58 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-27 16:55:59 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-27 16:55:59 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-27 16:55:59 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-27 16:55:59 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-27 16:55:59 - EnhancedWorkflowEngine - WARNING - Workflow cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5 execution was cancelled!
2025-06-27 16:55:59 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-27 16:55:59 - KafkaWorkflowConsumer - WARNING - Workflow execution for '50604f34-6f45-4b9d-a32c-d16691a2d80a' was cancelled
2025-06-27 16:55:59 - KafkaWorkflowConsumer - INFO - Workflow '50604f34-6f45-4b9d-a32c-d16691a2d80a' final status: cancelled, result: Workflow '50604f34-6f45-4b9d-a32c-d16691a2d80a' execution was cancelled
2025-06-27 16:55:59 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5, response: {'status': 'Workflow Cancelled', 'result': "Workflow '50604f34-6f45-4b9d-a32c-d16691a2d80a' execution was cancelled", 'workflow_status': 'cancelled'}
2025-06-27 16:55:59 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: cb3c69de-5d1f-41b8-8de6-bcbec64eaaa5 
2025-06-27 16:55:59 - Main - ERROR - Shutting down due to keyboard interrupt...
