import pytest
import logging
from unittest.mock import Mock, patch
from app.utils.enhanced_logger import (
    EnhancedLogFilter,
    setup_logging,
    get_log_level,
    get_logger,
    EXECUTE_LEVEL
)


class TestEnhancedLogger:
    """
    Test suite for enhanced logging functionality.
    Tests custom log levels, filtering, and logger configuration.
    """

    @pytest.fixture
    def root_logger(self):
        """
        Provides a clean root logger for testing.
        """
        logger = logging.getLogger()
        # Store original handlers
        original_handlers = logger.handlers.copy()
        original_level = logger.level
        
        # Clear handlers for testing
        logger.handlers.clear()
        
        yield logger
        
        # Restore original state
        logger.handlers = original_handlers
        logger.setLevel(original_level)

    def test_execute_log_level_registration(self):
        """
        Test that the EXECUTE log level is properly registered.
        """
        assert EXECUTE_LEVEL == 25
        assert logging.getLevelName(EXECUTE_LEVEL) == "EXECUTE"

    def test_execute_logging(self, root_logger):
        """
        Test the custom execute logging method.
        """
        logger = get_logger("test_execute")
        logger.setLevel(EXECUTE_LEVEL)

        # Add a mock handler to capture logs
        mock_handler = Mock()
        mock_handler.level = 0  # Set level to capture all logs
        logger.addHandler(mock_handler)

        # Test execute logging
        logger.execute("Test execute message")

        # Verify the log was captured with correct level
        mock_handler.handle.assert_called_once()
        log_record = mock_handler.handle.call_args[0][0]
        assert log_record.levelno == EXECUTE_LEVEL
        assert log_record.levelname == "EXECUTE"
        assert log_record.msg == "Test execute message"

    def test_enhanced_log_filter_allow_all(self):
        """
        Test EnhancedLogFilter with no restrictions.
        """
        log_filter = EnhancedLogFilter()
        record = logging.LogRecord(
            "test_logger", logging.INFO, "path", 0, "test message", (), None
        )
        
        assert log_filter.filter(record) is True

    def test_enhanced_log_filter_allowed_loggers(self):
        """
        Test EnhancedLogFilter with allowed loggers set.
        """
        allowed_loggers = {"allowed_logger"}
        log_filter = EnhancedLogFilter(allowed_loggers=allowed_loggers)

        # Test allowed logger
        allowed_record = logging.LogRecord(
            "allowed_logger", logging.INFO, "path", 0, "test message", (), None
        )
        assert log_filter.filter(allowed_record) is True

        # Test non-allowed logger
        blocked_record = logging.LogRecord(
            "blocked_logger", logging.INFO, "path", 0, "test message", (), None
        )
        assert log_filter.filter(blocked_record) is False

    def test_enhanced_log_filter_excluded_loggers(self):
        """
        Test EnhancedLogFilter with excluded loggers set.
        """
        excluded_loggers = {"excluded_logger"}
        log_filter = EnhancedLogFilter(excluded_loggers=excluded_loggers)

        # Test excluded logger
        excluded_record = logging.LogRecord(
            "excluded_logger", logging.INFO, "path", 0, "test message", (), None
        )
        assert log_filter.filter(excluded_record) is False

        # Test non-excluded logger
        allowed_record = logging.LogRecord(
            "allowed_logger", logging.INFO, "path", 0, "test message", (), None
        )
        assert log_filter.filter(allowed_record) is True

    @patch('app.utils.enhanced_logger.settings')
    def test_get_log_level_debug(self, mock_settings):
        """
        Test get_log_level when debug is enabled.
        """
        mock_settings.app_debug = True
        assert get_log_level() == logging.DEBUG

    @patch('app.utils.enhanced_logger.settings')
    def test_get_log_level_info(self, mock_settings):
        """
        Test get_log_level when debug is disabled.
        """
        mock_settings.app_debug = False
        assert get_log_level() == logging.INFO

    def test_setup_logging_basic(self, root_logger):
        """
        Test basic logging setup without filters.
        """
        setup_logging(enable_file_logging=False)

        assert len(root_logger.handlers) == 1
        handler = root_logger.handlers[0]
        assert isinstance(handler, logging.StreamHandler)
        assert handler.formatter is not None

    def test_setup_logging_with_filters(self, root_logger):
        """
        Test logging setup with allowed and excluded loggers.
        """
        allowed_loggers = {"test_allowed"}
        excluded_loggers = {"test_excluded"}
        
        setup_logging(
            allowed_loggers=allowed_loggers,
            excluded_loggers=excluded_loggers,
            enable_file_logging=False
        )
        
        handler = root_logger.handlers[0]
        assert len(handler.filters) == 1
        log_filter = handler.filters[0]
        assert isinstance(log_filter, EnhancedLogFilter)
        assert log_filter.allowed_loggers == allowed_loggers
        assert log_filter.excluded_loggers == excluded_loggers

    def test_setup_logging_custom_format(self, root_logger):
        """
        Test logging setup with custom format.
        """
        custom_format = "%(levelname)s - %(message)s"
        custom_date_format = "%H:%M:%S"
        
        setup_logging(
            log_format=custom_format,
            date_format=custom_date_format,
            enable_file_logging=False
        )
        
        handler = root_logger.handlers[0]
        formatter = handler.formatter
        assert formatter._fmt == custom_format
        assert formatter.datefmt == custom_date_format

    def test_get_logger_propagation(self):
        """
        Test logger creation with proper propagation settings.
        """
        logger = get_logger("test_logger")
        assert logger.name == "test_logger"
        assert logger.propagate is True

    def test_setup_logging_clears_existing_handlers(self, root_logger):
        """
        Test that setup_logging clears existing handlers before adding new ones.
        """
        # Record initial handler count (pytest may add handlers)
        initial_count = len(root_logger.handlers)

        # Add a dummy handler
        root_logger.addHandler(logging.StreamHandler())
        assert len(root_logger.handlers) == initial_count + 1

        # Setup logging
        setup_logging(enable_file_logging=False)

        # Verify only one handler exists (our new one)
        assert len(root_logger.handlers) == 1

    def test_setup_logging_with_file_logging(self, root_logger, tmp_path):
        """
        Test logging setup with file logging enabled.
        """
        import os
        original_cwd = os.getcwd()

        try:
            # Change to temp directory for testing
            os.chdir(tmp_path)

            setup_logging(enable_file_logging=True, log_file_name="test.log")

            # Should have both console and file handlers
            assert len(root_logger.handlers) == 2

            # Check handler types
            handler_types = [type(handler).__name__ for handler in root_logger.handlers]
            assert "StreamHandler" in handler_types
            assert "FileHandler" in handler_types

            # Check that log file was created
            logs_dir = tmp_path / "logs"
            assert logs_dir.exists()
            assert (logs_dir / "test.log").exists()

        finally:
            # Restore original working directory
            os.chdir(original_cwd)

    def test_enhanced_log_filter_complex_scenario(self):
        """
        Test EnhancedLogFilter with both allowed and excluded loggers.
        """
        allowed_loggers = {"allowed1", "allowed2"}
        excluded_loggers = {"excluded1", "allowed1"}  # Note the overlap
        log_filter = EnhancedLogFilter(
            allowed_loggers=allowed_loggers,
            excluded_loggers=excluded_loggers
        )

        # Test various scenarios
        test_cases = [
            ("allowed1", False),  # In both allowed and excluded -> excluded wins
            ("allowed2", True),   # In allowed only -> allowed
            ("excluded1", False), # In excluded -> excluded
            ("other", False),     # In neither -> blocked (due to allowed list)
        ]

        for logger_name, expected_result in test_cases:
            record = logging.LogRecord(
                logger_name, logging.INFO, "path", 0, "test message", (), None
            )
            assert log_filter.filter(record) is expected_result, \
                f"Failed for logger: {logger_name}"