<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">15%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-27 12:09 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app/__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce___init___py.html">app/config/__init__.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t8">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t8"><data value='Settings'>Settings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t58">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t58"><data value='SettingsManager'>SettingsManager</data></a></td>
                <td>43</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="18 43">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t200">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t200"><data value='SettingsProxy'>SettingsProxy</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>88</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="85 88">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b___init___py.html">app/core_/__init__.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t34">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t34"><data value='MCPClient'>MCPClient</data></a></td>
                <td>601</td>
                <td>503</td>
                <td>0</td>
                <td class="right" data-ratio="98 601">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>158</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="70 158">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t17">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t17"><data value='CustomSSHMCPClient'>CustomSSHMCPClient</data></a></td>
                <td>81</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t200">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t200"><data value='CustomSSHReadStream'>CustomSSHReadStream</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t225">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t225"><data value='CustomSSHWriteStream'>CustomSSHWriteStream</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t13">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t13"><data value='DirectSSHReadStream'>DirectSSHReadStream</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t82">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t82"><data value='DirectSSHWriteStream'>DirectSSHWriteStream</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t134">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t134"><data value='DirectMCPSession'>DirectMCPSession</data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html">app/core_/error_handler.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="0 75">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t13">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t13"><data value='ErrorCategory'>ErrorCategory</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t24">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t24"><data value='ErrorCode'>ErrorCode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t62">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t62"><data value='MCPExecutorError'>MCPExecutorError</data></a></td>
                <td>20</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="8 20">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t119">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t119"><data value='MCPConfigNotFoundError'>MCPConfigNotFoundError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t134">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t134"><data value='MCPConfigInvalidError'>MCPConfigInvalidError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t152">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t152"><data value='ContainerCreationError'>ContainerCreationError</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t175">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t175"><data value='ContainerExecutionError'>ContainerExecutionError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t195">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t195"><data value='SSHConnectionError'>SSHConnectionError</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t218">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t218"><data value='MCPServerUnreachableError'>MCPServerUnreachableError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t236">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t236"><data value='MCPAuthenticationError'>MCPAuthenticationError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t254">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t254"><data value='MCPToolExecutionError'>MCPToolExecutionError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t272">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t272"><data value='MCPHTTPMethodError'>MCPHTTPMethodError</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t292">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t292"><data value='PayloadValidationError'>PayloadValidationError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t310">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t310"><data value='CredentialRetrievalError'>CredentialRetrievalError</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>65</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="62 65">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t31">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t31"><data value='HealthStatus'>HealthStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t41">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t41"><data value='HealthCheckResult'>HealthCheckResult</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t57">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t57"><data value='SystemHealth'>SystemHealth</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t71">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t71"><data value='HealthChecker'>HealthChecker</data></a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t25">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t25"><data value='InfiniteSemaphore'>InfiniteSemaphore</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t35">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t35"><data value='KafkaMCPService'>KafkaMCPService</data></a></td>
                <td>314</td>
                <td>314</td>
                <td>0</td>
                <td class="right" data-ratio="0 314">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html#t41">app/core_/mcp_executor.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html#t41"><data value='MCPExecutor'>MCPExecutor</data></a></td>
                <td>330</td>
                <td>330</td>
                <td>0</td>
                <td class="right" data-ratio="0 330">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html">app/core_/mcp_executor.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t17">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t17"><data value='ExecutionType'>ExecutionType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t24">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t24"><data value='ExecutionStatus'>ExecutionStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t32">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t32"><data value='MetricType'>MetricType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t41">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t41"><data value='ExecutionMetric'>ExecutionMetric</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t60">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t60"><data value='ConfigMetric'>ConfigMetric</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t76">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t76"><data value='AuthenticationMetric'>AuthenticationMetric</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t92">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t92"><data value='MetricsLogger'>MetricsLogger</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="0 74">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="0 141">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_authentication_manager_py.html#t7">app/schemas/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_authentication_manager_py.html#t7"><data value='AuthenticationType'>AuthenticationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_authentication_manager_py.html#t18">app/schemas/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_authentication_manager_py.html#t18"><data value='TokenValidationError'>TokenValidationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_authentication_manager_py.html#t25">app/schemas/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_authentication_manager_py.html#t25"><data value='AuthenticationConfig'>AuthenticationConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_authentication_manager_py.html">app/schemas/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_authentication_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html#t5">app/schemas/client.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html#t5"><data value='ConnectionConfig'>ConnectionConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html#t17">app/schemas/client.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html#t17"><data value='MCPClientError'>MCPClientError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html#t23">app/schemas/client.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html#t23"><data value='AuthenticationError'>AuthenticationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html#t29">app/schemas/client.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html#t29"><data value='ConnectionError'>ConnectionError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html#t35">app/schemas/client.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html#t35"><data value='ProtocolError'>ProtocolError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html">app/schemas/client.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html">app/services/__init__.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t12">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t12"><data value='AuthenticationManager'>AuthenticationManager</data></a></td>
                <td>95</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="12 95">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t20">app/services/container_client.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t20"><data value='ContainerManagementClient'>ContainerManagementClient</data></a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="0 141">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html">app/services/container_client.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t11">app/services/credential_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t11"><data value='CredentialService'>CredentialService</data></a></td>
                <td>72</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html">app/services/credential_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t17">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t17"><data value='SecureSSHKeyManager'>SecureSSHKeyManager</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t157">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t157"><data value='GlobalSSHKeyManager'>GlobalSSHKeyManager</data></a></td>
                <td>201</td>
                <td>201</td>
                <td>0</td>
                <td class="right" data-ratio="0 201">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="35 48">73%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>3140</td>
                <td>2682</td>
                <td>0</td>
                <td class="right" data-ratio="458 3140">15%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-27 12:09 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
