{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "c6548fe6ef1f5b9abc86a053bd99ec9d", "files": {"z_5f5a17c013354698___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_5f5a17c013354698___init___py.html", "file": "app/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_92724abef7b332ce___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_92724abef7b332ce___init___py.html", "file": "app/config/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_92724abef7b332ce_config_py": {"hash": "84549df90cc9e8c049df4e7c010a170f", "index": {"url": "z_92724abef7b332ce_config_py.html", "file": "app/config/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fc1582fcccf5ee0b___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_fc1582fcccf5ee0b___init___py.html", "file": "app/core_/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fc1582fcccf5ee0b_client_py": {"hash": "415317c116089353a7d44c2f301086a7", "index": {"url": "z_fc1582fcccf5ee0b_client_py.html", "file": "app/core_/client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 759, "n_excluded": 0, "n_missing": 591, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fc1582fcccf5ee0b_custom_ssh_client_py": {"hash": "add03d537addb1c4108a46b605ad9cfd", "index": {"url": "z_fc1582fcccf5ee0b_custom_ssh_client_py.html", "file": "app/core_/custom_ssh_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 127, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fc1582fcccf5ee0b_direct_ssh_streams_py": {"hash": "95f6a8a4c5a4fd3985931cedd227d9b3", "index": {"url": "z_fc1582fcccf5ee0b_direct_ssh_streams_py.html", "file": "app/core_/direct_ssh_streams.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 131, "n_excluded": 0, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fc1582fcccf5ee0b_error_handler_py": {"hash": "233124bcf5260a5dcd969ff4955fa7d8", "index": {"url": "z_fc1582fcccf5ee0b_error_handler_py.html", "file": "app/core_/error_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 75, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fc1582fcccf5ee0b_exceptions_py": {"hash": "7bc5f11970d2102199f73545e31de15e", "index": {"url": "z_fc1582fcccf5ee0b_exceptions_py.html", "file": "app/core_/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fc1582fcccf5ee0b_health_py": {"hash": "7ebba2106d7d8ef665c000d39243c2fa", "index": {"url": "z_fc1582fcccf5ee0b_health_py.html", "file": "app/core_/health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 129, "n_excluded": 0, "n_missing": 129, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fc1582fcccf5ee0b_kafka_service_py": {"hash": "95580f56dccf9d9c54b71941177ee00a", "index": {"url": "z_fc1582fcccf5ee0b_kafka_service_py.html", "file": "app/core_/kafka_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 350, "n_excluded": 0, "n_missing": 350, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fc1582fcccf5ee0b_mcp_executor_py": {"hash": "d339f7f7012a479a4b13e3aaad7f2199", "index": {"url": "z_fc1582fcccf5ee0b_mcp_executor_py.html", "file": "app/core_/mcp_executor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 349, "n_excluded": 0, "n_missing": 349, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fc1582fcccf5ee0b_metrics_py": {"hash": "0ecee10c94601c519b52503021b37d6d", "index": {"url": "z_fc1582fcccf5ee0b_metrics_py.html", "file": "app/core_/metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 113, "n_excluded": 0, "n_missing": 113, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_main_py": {"hash": "e51950e34faea7471ccfadba678db0ff", "index": {"url": "z_5f5a17c013354698_main_py.html", "file": "app/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 141, "n_excluded": 0, "n_missing": 141, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_authentication_manager_py": {"hash": "9290740fe6252894ea97f7306e92d4c4", "index": {"url": "z_c0f67d75e686303c_authentication_manager_py.html", "file": "app/schemas/authentication_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_client_py": {"hash": "39f9c208b095a630ed57285f23560801", "index": {"url": "z_c0f67d75e686303c_client_py.html", "file": "app/schemas/client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69___init___py": {"hash": "ad1c9e22450e5f51ddc5d2d056162e89", "index": {"url": "z_c318f3fa19a49f69___init___py.html", "file": "app/services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_authentication_manager_py": {"hash": "cd86213867d5d9bd7b7ea93652bf7b68", "index": {"url": "z_c318f3fa19a49f69_authentication_manager_py.html", "file": "app/services/authentication_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 115, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_container_client_py": {"hash": "0c33e509c8777328f2261178460f62e7", "index": {"url": "z_c318f3fa19a49f69_container_client_py.html", "file": "app/services/container_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 156, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_credential_service_py": {"hash": "7786c5e0fad8a6b5ce336d1a5cb10555", "index": {"url": "z_c318f3fa19a49f69_credential_service_py.html", "file": "app/services/credential_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 87, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_ssh_manager_py": {"hash": "0c73387fa395988830fe98cf9482992d", "index": {"url": "z_c318f3fa19a49f69_ssh_manager_py.html", "file": "app/services/ssh_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 299, "n_excluded": 0, "n_missing": 264, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}