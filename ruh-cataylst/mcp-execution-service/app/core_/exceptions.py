#!/usr/bin/env python3
"""
Custom exception classes for MCP Executor Service.

This module defines specific exception types for different failure scenarios
to improve error handling and debugging throughout the application.
"""

from typing import Optional, Dict, Any
from enum import Enum


class ErrorCategory(Enum):
    """Error categories for Kafka error propagation."""

    VALIDATION_ERROR = "validation_error"
    AUTHENTICATION_ERROR = "authentication_error"
    INFRASTRUCTURE_ERROR = "infrastructure_error"
    MCP_ERROR = "mcp_error"
    SYSTEM_ERROR = "system_error"
    KAFKA_ERROR = "kafka_error"


class ErrorCode(Enum):
    """Specific error codes for programmatic handling."""

    # Validation errors
    INVALID_PAYLOAD = "invalid_payload"
    MISSING_REQUIRED_FIELD = "missing_required_field"
    INVALID_FIELD_TYPE = "invalid_field_type"

    # Authentication errors
    CREDENTIAL_NOT_FOUND = "credential_not_found"
    CREDENTIAL_INVALID = "credential_invalid"
    AUTHENTICATION_FAILED = "authentication_failed"
    TOKEN_EXPIRED = "token_expired"

    # Infrastructure errors
    CONTAINER_CREATION_FAILED = "container_creation_failed"
    CONTAINER_EXECUTION_FAILED = "container_execution_failed"
    SSH_CONNECTION_FAILED = "ssh_connection_failed"
    NETWORK_ERROR = "network_error"

    # MCP errors
    MCP_SERVER_UNREACHABLE = "mcp_server_unreachable"
    MCP_TOOL_NOT_FOUND = "mcp_tool_not_found"
    MCP_EXECUTION_FAILED = "mcp_execution_failed"
    MCP_TIMEOUT = "mcp_timeout"
    MCP_HTTP_METHOD_ERROR = "mcp_http_method_error"

    # System errors
    CONFIGURATION_ERROR = "configuration_error"
    SERVICE_UNAVAILABLE = "service_unavailable"
    INTERNAL_ERROR = "internal_error"

    # Kafka errors
    KAFKA_SEND_FAILED = "kafka_send_failed"
    KAFKA_CONNECTION_FAILED = "kafka_connection_failed"
    MESSAGE_DECODE_FAILED = "message_decode_failed"


class MCPExecutorError(Exception):
    """Base exception class for all MCP Executor errors."""

    def __init__(
        self,
        message: str,
        error_category: ErrorCategory = ErrorCategory.SYSTEM_ERROR,
        error_code: ErrorCode = ErrorCode.INTERNAL_ERROR,
        details: Optional[Dict[str, Any]] = None,
        retryable: bool = False,
    ):
        super().__init__(message)
        self.message = message
        self.error_category = error_category
        self.error_code = error_code
        self.details = details or {}
        self.retryable = retryable

    def __str__(self) -> str:
        if self.details:
            return f"{self.message} | Details: {self.details}"
        return self.message

    def to_kafka_error(self) -> Dict[str, Any]:
        """Convert exception to Kafka-safe error response (no stack traces)."""
        return {
            "error_type": self.error_category.value,
            "error_code": self.error_code.value,
            "error_message": self.message,
            "retryable": self.retryable,
            "details": self._sanitize_details(),
        }

    def _sanitize_details(self) -> Dict[str, Any]:
        """Sanitize details for Kafka (remove sensitive information)."""
        sanitized = {}
        for key, value in self.details.items():
            # Skip sensitive keys
            key_lower = key.lower()
            sensitive_keywords = [
                "password",
                "token",
                "secret",
                "key",
                "credential",
                "auth",
            ]
            if any(keyword in key_lower for keyword in sensitive_keywords):
                sanitized[key] = "[REDACTED]"
            elif isinstance(value, str) and len(value) > 500:
                # Truncate very long strings
                sanitized[key] = value[:500] + "... [TRUNCATED]"
            else:
                sanitized[key] = value
        return sanitized


class MCPConfigNotFoundError(MCPExecutorError):
    """Raised when MCP configuration cannot be found or retrieved."""

    def __init__(self, mcp_id: str, details: Optional[Dict[str, Any]] = None):
        message = f"MCP configuration not found for mcp_id: {mcp_id}"
        super().__init__(
            message,
            error_category=ErrorCategory.SYSTEM_ERROR,
            error_code=ErrorCode.CONFIGURATION_ERROR,
            details=details,
            retryable=False,
        )
        self.mcp_id = mcp_id


class MCPConfigInvalidError(MCPExecutorError):
    """Raised when MCP configuration is invalid or malformed."""

    def __init__(
        self, mcp_id: str, reason: str, details: Optional[Dict[str, Any]] = None
    ):
        message = f"Invalid MCP configuration for mcp_id: {mcp_id} - {reason}"
        super().__init__(
            message,
            error_category=ErrorCategory.SYSTEM_ERROR,
            error_code=ErrorCode.CONFIGURATION_ERROR,
            details=details,
            retryable=False,
        )
        self.mcp_id = mcp_id
        self.reason = reason


class ContainerCreationError(MCPExecutorError):
    """Raised when container creation fails."""

    def __init__(
        self,
        mcp_id: str,
        user_id: str,
        reason: str,
        details: Optional[Dict[str, Any]] = None,
    ):
        message = f"Container creation failed for mcp_id: {mcp_id}, user_id: {user_id} - {reason}"
        super().__init__(
            message,
            error_category=ErrorCategory.INFRASTRUCTURE_ERROR,
            error_code=ErrorCode.CONTAINER_CREATION_FAILED,
            details=details,
            retryable=True,
        )
        self.mcp_id = mcp_id
        self.user_id = user_id
        self.reason = reason


class ContainerExecutionError(MCPExecutorError):
    """Raised when container execution fails."""

    def __init__(
        self, container_id: str, reason: str, details: Optional[Dict[str, Any]] = None
    ):
        message = (
            f"Container execution failed for container_id: {container_id} - {reason}"
        )
        super().__init__(
            message,
            error_category=ErrorCategory.INFRASTRUCTURE_ERROR,
            error_code=ErrorCode.CONTAINER_EXECUTION_FAILED,
            details=details,
            retryable=True,
        )
        self.container_id = container_id
        self.reason = reason


class SSHConnectionError(MCPExecutorError):
    """Raised when SSH connection fails."""

    def __init__(
        self,
        ssh_host: str,
        ssh_user: str,
        reason: str,
        details: Optional[Dict[str, Any]] = None,
    ):
        message = f"SSH connection failed to {ssh_user}@{ssh_host} - {reason}"
        super().__init__(
            message,
            error_category=ErrorCategory.INFRASTRUCTURE_ERROR,
            error_code=ErrorCode.SSH_CONNECTION_FAILED,
            details=details,
            retryable=True,
        )
        self.ssh_host = ssh_host
        self.ssh_user = ssh_user
        self.reason = reason


class MCPServerUnreachableError(MCPExecutorError):
    """Raised when MCP server is unreachable via URL."""

    def __init__(
        self, server_url: str, reason: str, details: Optional[Dict[str, Any]] = None
    ):
        message = f"MCP server unreachable at {server_url} - {reason}"
        super().__init__(
            message,
            error_category=ErrorCategory.MCP_ERROR,
            error_code=ErrorCode.MCP_SERVER_UNREACHABLE,
            details=details,
            retryable=True,
        )
        self.server_url = server_url
        self.reason = reason


class MCPAuthenticationError(MCPExecutorError):
    """Raised when MCP authentication fails."""

    def __init__(
        self, server_url: str, reason: str, details: Optional[Dict[str, Any]] = None
    ):
        message = f"MCP authentication failed for {server_url} - {reason}"
        super().__init__(
            message,
            error_category=ErrorCategory.AUTHENTICATION_ERROR,
            error_code=ErrorCode.AUTHENTICATION_FAILED,
            details=details,
            retryable=False,
        )
        self.server_url = server_url
        self.reason = reason


class MCPToolExecutionError(MCPExecutorError):
    """Raised when MCP tool execution fails."""

    def __init__(
        self, tool_name: str, reason: str, details: Optional[Dict[str, Any]] = None
    ):
        message = f"MCP tool execution failed for tool: {tool_name} - {reason}"
        super().__init__(
            message,
            error_category=ErrorCategory.MCP_ERROR,
            error_code=ErrorCode.MCP_EXECUTION_FAILED,
            details=details,
            retryable=True,
        )
        self.tool_name = tool_name
        self.reason = reason


class MCPHTTPMethodError(MCPExecutorError):
    """Raised when MCP server returns HTTP method errors like 405 Method Not Allowed."""

    def __init__(
        self, server_url: str, method: str, status_code: int, reason: str, details: Optional[Dict[str, Any]] = None
    ):
        message = f"HTTP {status_code} error for {method} request to {server_url}: {reason}"
        super().__init__(
            message,
            error_category=ErrorCategory.MCP_ERROR,
            error_code=ErrorCode.MCP_HTTP_METHOD_ERROR,
            details=details,
            retryable=False,  # HTTP method errors are not retryable
        )
        self.server_url = server_url
        self.method = method
        self.status_code = status_code
        self.reason = reason


class PayloadValidationError(MCPExecutorError):
    """Raised when payload validation fails."""

    def __init__(
        self, field: str, reason: str, details: Optional[Dict[str, Any]] = None
    ):
        message = f"Payload validation failed for field: {field} - {reason}"
        super().__init__(
            message,
            error_category=ErrorCategory.VALIDATION_ERROR,
            error_code=ErrorCode.INVALID_PAYLOAD,
            details=details,
            retryable=False,
        )
        self.field = field
        self.reason = reason


class CredentialRetrievalError(MCPExecutorError):
    """Raised when credential retrieval fails."""

    def __init__(
        self,
        user_id: str,
        mcp_id: str,
        reason: str,
        details: Optional[Dict[str, Any]] = None,
    ):
        message = f"Credential retrieval failed for user_id: {user_id}, mcp_id: {mcp_id} - {reason}"
        super().__init__(
            message,
            error_category=ErrorCategory.AUTHENTICATION_ERROR,
            error_code=ErrorCode.CREDENTIAL_NOT_FOUND,
            details=details,
            retryable=True,
        )
        self.user_id = user_id
        self.mcp_id = mcp_id
        self.reason = reason


# Exception mapping for easier error handling
EXCEPTION_MAPPING = {
    "mcp_config_not_found": MCPConfigNotFoundError,
    "mcp_config_invalid": MCPConfigInvalidError,
    "container_creation_failed": ContainerCreationError,
    "container_execution_failed": ContainerExecutionError,
    "ssh_connection_failed": SSHConnectionError,
    "mcp_server_unreachable": MCPServerUnreachableError,
    "mcp_authentication_failed": MCPAuthenticationError,
    "mcp_tool_execution_failed": MCPToolExecutionError,
    "mcp_http_method_error": MCPHTTPMethodError,
    "payload_validation_failed": PayloadValidationError,
    "credential_retrieval_failed": CredentialRetrievalError,
}


def get_exception_class(error_type: str) -> type:
    """Get exception class by error type string."""
    return EXCEPTION_MAPPING.get(error_type, MCPExecutorError)


def create_exception(error_type: str, *args, **kwargs) -> MCPExecutorError:
    """Create exception instance by error type string."""
    exception_class = get_exception_class(error_type)
    return exception_class(*args, **kwargs)
