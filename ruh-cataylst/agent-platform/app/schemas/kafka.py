from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class AgentType(str, Enum):
    """Enum for agent types"""

    COMPONENT = "component"
    ORCHESTRATION = "orchestration"
    PERSONA = "persona"
    AI_AGENT = "ai_agent"
    # Additional types from existing constants
    GLOBAL_AGENT = "global_agent"
    PERSONA_AGENT = "persona_agent"
    USER_AGENT = "user_agent"
    HEAD_AGENT = "head_agent"


class ExecutionType(str, Enum):
    """Enum for execution types"""

    RESPONSE = "response"
    STREAMING = "streaming"
    BATCH = "batch"


class ModelConfig(BaseModel):
    """Model configuration schema"""

    model_provider: str = Field(
        ..., description="Model provider (e.g., OpenAI, Anthropic)"
    )
    model: str = Field(..., description="Model name (e.g., gpt-4o, claude-3)")
    temperature: Optional[float] = Field(0.7, description="Model temperature")
    max_tokens: Optional[int] = Field(1000, description="Maximum tokens")


class McpConfig(BaseModel):
    """MCP (Model Context Protocol) configuration"""

    mcp_id: str = Field(..., description="MCP server ID")
    tool_name: str = Field(..., description="Tool name to use from MCP server")


class EnhancedAgentConfig(BaseModel):
    """Enhanced agent configuration matching the provided structure"""

    id: str = Field(..., description="Agent ID")
    name: str = Field(..., description="Agent name")
    description: str = Field(..., description="Agent description")
    system_message: str = Field(..., description="System message for the agent")
    llm_model_config: ModelConfig = Field(
        ..., description="Model configuration", alias="model_config"
    )
    mcps: Optional[List[McpConfig]] = Field(
        default_factory=list, description="MCP configurations"
    )


class MessageAttachment(BaseModel):
    """Schema for message attachments (images, documents, etc.)"""

    file_name: str = Field(..., description="Name of the attached file")
    file_type: str = Field(
        ..., description="MIME type of the file (e.g., 'image/jpeg', 'application/pdf')"
    )
    file_size: int = Field(..., description="Size of the file in bytes")
    file_data: Optional[str] = Field(
        None,
        description="Base64 encoded file content (use either file_data or file_url)",
    )
    file_url: Optional[str] = Field(
        None,
        description="URL to the file if stored externally (use either file_data or file_url)",
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata about the file"
    )

    def model_post_init(self, __context) -> None:
        """Validate that either file_data or file_url is provided, but not both"""
        if not self.file_data and not self.file_url:
            raise ValueError("Either file_data or file_url must be provided")
        if self.file_data and self.file_url:
            raise ValueError("Provide either file_data or file_url, not both")


class AgentCreationRequest(BaseModel):
    agent_id: str
    user_id: str
    communication_type: str
    run_id: str
    organization_id: Optional[str] = None
    use_knowledge: Optional[bool] = False
    agent_group_id: Optional[str] = None
    variables: Optional[dict] = None
    conversation_context: Optional[List[Dict[str, Any]]] = []


class AgentChatRequest(BaseModel):
    run_id: str
    session_id: str
    chat_context: list[dict[str, str]]
    chat_response: Optional[str] = "message"
    attachments: Optional[List[MessageAttachment]] = Field(
        default_factory=list, description="List of file attachments with the message"
    )


class AgentChatResponse(BaseModel):
    run_id: str
    session_id: str
    event_type: Optional[str] = None
    agent_response: Optional[dict] = None
    success: bool = True
    message: Optional[str] = None
    final: Optional[bool] = False
    stream_chunk_id: Optional[int] = None  # For ordering streaming chunks
    timestamp: Optional[str] = None  # For timing information
    event_type: Optional[str] = None
    error: Optional[str] = None
    agent_type: Optional[str] = None
    request_id: Optional[str] = None


class AgentMessageRequest(BaseModel):
    """
    Schema for direct agent message processing with provided agent config.
    This allows sending messages directly to agents without requiring
    pre-created sessions.
    """

    request_id: str = Field(..., description="Unique request identifier")
    user_id: str = Field(..., description="Identifier of the user sending the message")
    correlation_id: str = Field(..., description="Correlation ID for tracking")
    agent_type: AgentType = Field(..., description="Type of agent")
    execution_type: ExecutionType = Field(..., description="Type of execution")
    query: str = Field(..., description="The user's message/query to send to the agent")
    variables: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Variables for substitution"
    )
    agent_config: EnhancedAgentConfig = Field(
        ..., description="Enhanced agent configuration"
    )

    # Optional fields for backward compatibility
    run_id: Optional[str] = None  # Unique identifier for the agent run session
    organization_id: Optional[str] = None  # Organization identifier
    use_knowledge: Optional[bool] = False  # Whether to enable knowledge tools
    attachments: Optional[List[MessageAttachment]] = Field(
        default_factory=list, description="List of file attachments with the message"
    )
    mode: Optional[str] = None
    agent_id: Optional[str] = None
    response_routing: Optional[str] = None


class AgentSessionDeletionRequest(BaseModel):
    """
    Schema for agent session deletion requests.
    This allows deleting agent sessions once chat is complete.
    """

    session_id: str  # Session ID to delete
    run_id: str  # Unique identifier for the deletion request
    user_id: str  # Identifier of the user requesting deletion
    reason: Optional[str] = "chat_complete"  # Reason for deletion
    force: Optional[bool] = False  # Force deletion even if session is active


class AgentSessionDeletionResponse(BaseModel):
    """
    Schema for agent session deletion responses.
    """

    run_id: str  # Request identifier
    session_id: str  # Session that was deleted
    success: bool = True  # Whether deletion was successful
    message: Optional[str] = None  # Status message
    deleted_at: Optional[str] = None  # Timestamp of deletion


class HumanInputRequest(BaseModel):
    """
    Schema for requesting human input during team conversations.
    """

    session_id: str  # Session ID where input is needed
    run_id: str  # Current run ID
    team_conversation_id: str  # Unique ID for this team conversation
    prompt: str  # The prompt/question for the human
    timeout_seconds: Optional[int] = (
        300  # Timeout for human response (5 minutes default)
    )
    requesting_agent: Optional[str] = None  # Which agent is requesting input


class HumanInputResponse(BaseModel):
    """
    Schema for human input responses during team conversations.
    """

    session_id: str  # Session ID
    run_id: str  # Current run ID
    team_conversation_id: str  # Unique ID for this team conversation
    user_input: str  # The human's input/response


class OrchestrationTeamSessionRequest(BaseModel):
    """
    Schema for creating orchestration team sessions.
    """

    run_id: str
    user_id: str
    organization_id: Optional[str] = None
    variables: Optional[Dict[str, Any]] = Field(default_factory=dict)
    communication_type: str = "orchestration_team"
    conversation_context: Optional[List[Dict[str, Any]]] = []


class OrchestrationTeamChatRequest(BaseModel):
    """
    Schema for orchestration team chat requests with human-in-the-loop support.
    """

    run_id: str
    session_id: Optional[str] = None  # If None, will create new session
    user_id: str  # Required for session creation if session_id is None
    user_message: str
    organization_id: Optional[str] = None
    variables: Optional[Dict[str, Any]] = Field(default_factory=dict)
    attachments: Optional[List[MessageAttachment]] = Field(
        default_factory=list, description="List of file attachments with the message"
    )
    mode: Optional[str] = "ASK"  # "ASK" or "ACT"
    tools: Optional[List[str]] = []


class AgentChatStopRequest(BaseModel):
    """
    Schema for agent chat stop requests.
    This allows stopping any running agent chat session.
    """

    session_id: Optional[str] = None  # Specific session to stop
    run_id: str  # Unique identifier for the stop request
    user_id: str  # Identifier of the user requesting the stop
    stop_type: Optional[str] = "session"  # "session", "user_all", or "force_all"
    reason: Optional[str] = "user_requested"  # Reason for stopping


class AgentChatStopResponse(BaseModel):
    """
    Schema for agent chat stop responses.
    """

    run_id: str
    session_id: Optional[str] = None
    success: bool
    message: str
    stopped_sessions: Optional[List[str]] = Field(default_factory=list)
    stopped_at: Optional[str] = None


class AgentQueryRequest(BaseModel):
    """
    Enhanced schema for agent query requests with comprehensive input fields.
    """

    agent_id: str = Field(..., description="Agent ID to process the query")
    query: str = Field(..., description="The query/question to send to the agent")
    run_id: str = Field(..., description="Unique identifier for the query run")
    user_id: Optional[str] = Field(None, description="User identifier")
    organization_id: Optional[str] = Field(None, description="Organization identifier")
    variables: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Variables for substitution in the query"
    )

    # Enhanced input fields
    workflow_ids: Optional[List[str]] = Field(
        default_factory=list,
        description="List of workflow IDs to associate with the query",
    )
    mcp_tool_ids: Optional[List[str]] = Field(
        default_factory=list,
        description="List of MCP tool IDs for tool-specific execution",
    )
    use_knowledge: Optional[bool] = Field(
        False, description="Whether to enable knowledge retrieval for the query"
    )
    communication_type: Optional[str] = Field(
        "query", description="Type of communication (query, chat, etc.)"
    )
    agent_group_id: Optional[str] = Field(
        None, description="Agent group ID if using group agents"
    )
    conversation_context: Optional[List[Dict[str, Any]]] = Field(
        default_factory=list, description="Previous conversation context"
    )
    attachments: Optional[List[MessageAttachment]] = Field(
        default_factory=list, description="File attachments with the query"
    )
