"use client";

import React, { useEffect } from "react";
import { useUserStore } from "@/store/userStore";
import { useRouter, usePathname } from "next/navigation";
import { authApi } from "@/lib/authApi";
import { publicRoutes } from "@/shared/routes";
import { preloadCredentials } from "@/lib/credentialEnhancer";

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const user = useUserStore((state) => state.user);
  const setUser = useUserStore((state) => state.setUser);
  const clearUser = useUserStore((state) => state.clearUser);
  const router = useRouter();
  const pathname = usePathname();

  // Check if the current path is public
  const isPublicRoute = publicRoutes.some(
    (route) =>
      // For exact matches (like login, signup)
      pathname === route ||
      // For routes with parameters (like reset-password/[token])
      (route.startsWith("/") && pathname.startsWith(route)),
  );

  // Check if user is authenticated
  const isAuthenticated = !!user?.accessToken;

  useEffect(() => {
    // Try to get current user on initial load
    const loadUser = async () => {
      try {
        // Check if we have an access token in cookies
        const isAuth = await authApi.isAuthenticated();
        console.log("Authentication check result:", isAuth);

        if (isAuth) {
          try {
            // Try to get user data
            const userData = await authApi.getCurrentUser();
            console.log("User data retrieved successfully:", userData);

            // Get the access token from cookies
            const accessToken = await authApi.getAccessToken();

            // Update the user store
            setUser({
              ...userData,
              accessToken,
            });

            // Preload credentials after successful authentication
            preloadCredentials().catch(error => {
              console.warn('Failed to preload credentials:', error);
            });
          } catch (userError: any) {
            // Handle specific authentication errors
            if (userError.response?.status === 403 || userError.response?.status === 401) {
              console.log("Authentication error:", userError.response?.status);
              clearUser();

              // If we're not on a public route, redirect to login
              if (!isPublicRoute) {
                router.push("/login");
              }
            } else {
              // For other errors, just log them but don't clear user yet
              console.error("Error fetching user data:", userError);
            }
          }
        } else {
          // No authentication token found
          console.log("No authentication token found");
          clearUser();
        }
      } catch (error) {
        // If there's an error in the overall process, clear the user state
        clearUser();
        console.error("Failed to load user:", error);
      }
    };

    loadUser();
  }, [setUser, clearUser, router, isPublicRoute]);

  useEffect(() => {
    // Handle route protection
    if (!isPublicRoute && !isAuthenticated) {
      router.push(`/login`);
    }

    // Redirect authenticated users away from auth pages
    if (isPublicRoute && isAuthenticated) {
      router.push("/");
    }
  }, [pathname, isAuthenticated, isPublicRoute, router]);

  return <>{children}</>;
}
