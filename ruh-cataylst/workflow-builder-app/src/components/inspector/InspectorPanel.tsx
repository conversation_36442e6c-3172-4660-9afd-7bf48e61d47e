import React, { useEffect, useState } from "react";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import {
  isMCPMarketplaceComponent,
  MCPMarketplaceComponent,
} from "@/components/marketplace/MCPMarketplaceComponent";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { InfoIcon } from "lucide-react";

// Import the new decomposed components
import { InspectorProvider } from "./InspectorContext";
import { InspectorHeader } from "./InspectorHeader";
import { InspectorTabs } from "./InspectorTabs";
import { InspectorFooter } from "./InspectorFooter";
import { EmptyState } from "./EmptyState";
import { NotificationManager } from "./NotificationManager";

interface ApplicationData {
  providers: any[];
  models: any[];
  credentials: any[];
  isLoadingAppData: boolean;
  appDataError: string | null;
}

interface InspectorPanelProps {
  selectedNode: Node<WorkflowNodeData> | null;
  onNodeDataChange: (nodeId: string, newData: WorkflowNodeData) => void;
  onClose: () => void;
  onDeleteNode: (nodeId: string) => void;
  edges: Edge[]; // Add edges to track connections
  nodes: Node<WorkflowNodeData>[]; // Add nodes to get source node information
  setIsEditingField?: (isEditing: boolean) => void; // Optional callback to notify when editing fields
  applicationData?: ApplicationData; // Application data for providers, models, credentials
}

/**
 * Main inspector panel component that provides a UI for inspecting and configuring nodes
 */
export function InspectorPanel(props: InspectorPanelProps) {
  const { selectedNode, onNodeDataChange, onClose } = props;
  const isOpen = !!selectedNode;

  // Note: Credential loading is now handled by individual components using cached data
  // This removes the need to fetch credentials at the panel level

  return (
    <InspectorProvider {...props}>
      {/* Render the MCPMarketplaceComponent if the selected node is an MCP Marketplace component */}
      {selectedNode && isMCPMarketplaceComponent(selectedNode) && (
        <MCPMarketplaceComponent node={selectedNode} onNodeDataChange={onNodeDataChange} />
      )}

      <Drawer open={isOpen} onClose={onClose} direction="right">
        <DrawerContent className="bg-card/30 mt-0 ml-auto flex h-full w-[400px] flex-col overflow-hidden rounded-none backdrop-blur-sm">
          {selectedNode?.data?.definition ? (
            <>
              <InspectorHeader />
              <div className="space-y-2">
                <Alert variant="info" className="mx-4 mt-2 bg-blue-500/10 text-blue-700 dark:text-blue-400">
                  <InfoIcon className="h-4 w-4" />
                  <AlertDescription className="text-xs">
                    Fields are not validated during editing. Use the "Validate Node" button to check your inputs.
                  </AlertDescription>
                </Alert>
                <Alert variant="success" className="mx-4 mt-0 bg-green-500/10 text-green-700 dark:text-green-400">
                  <InfoIcon className="h-4 w-4" />
                  <AlertDescription className="text-xs">
                    Changes are applied immediately as you type. Click "Apply" to explicitly apply all changes.
                  </AlertDescription>
                </Alert>
              </div>
              <InspectorTabs />
              <InspectorFooter />
            </>
          ) : (
            <EmptyState />
          )}
        </DrawerContent>
      </Drawer>

      {/* Notification manager */}
      <NotificationManager />
    </InspectorProvider>
  );
}
